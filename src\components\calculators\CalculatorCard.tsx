"use client"

import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import { motion } from "framer-motion";
import { useTheme } from "@/hooks/useTheme";
import * as LucideIcons from "lucide-react";
import { categoryLabels } from "@/data/calculators";
import { useState } from "react";

interface CalculatorCardProps {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'finance' | 'math' | 'conversion' | 'health';
  index?: number;
}

export default function CalculatorCard({
  id,
  title,
  description,
  icon,
  category,
  index = 0,
}: CalculatorCardProps) {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const [isNavigating, setIsNavigating] = useState(false);

  // Calculate staggered delay based on index
  const delay = 0.05 * (index % 8); // Reset delay every 8 items for better UX

  const handleClick = () => {
    setIsNavigating(true);
    // Reset loading state after navigation
    setTimeout(() => setIsNavigating(false), 1000);
  };

  // Function to render the appropriate icon based on the icon name
  const renderIcon = (iconName: string, className: string = "w-6 h-6") => {
    // Convert kebab-case to PascalCase for Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    // Get the icon component from Lucide
    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    // Fallback to a default icon if the specified icon doesn't exist
    return <LucideIcons.Calculator className={className} />;
  };

  // Get category color based on category
  const getCategoryColor = () => {
    switch(category) {
      case 'finance':
        return isDark ? 'bg-blue-900/30 text-blue-300' : 'bg-blue-100 text-blue-700';
      case 'math':
        return isDark ? 'bg-purple-900/30 text-purple-300' : 'bg-purple-100 text-purple-700';
      case 'conversion':
        return isDark ? 'bg-green-900/30 text-green-300' : 'bg-green-100 text-green-700';
      case 'health':
        return isDark ? 'bg-red-900/30 text-red-300' : 'bg-red-100 text-red-700';
      default:
        return isDark ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-700';
    }
  };

  // Get icon color based on category
  const getIconColor = () => {
    switch(category) {
      case 'finance':
        return isDark ? 'text-blue-400 group-hover:text-blue-300' : 'text-blue-600 group-hover:text-blue-500';
      case 'math':
        return isDark ? 'text-purple-400 group-hover:text-purple-300' : 'text-purple-600 group-hover:text-purple-500';
      case 'conversion':
        return isDark ? 'text-green-400 group-hover:text-green-300' : 'text-green-600 group-hover:text-green-500';
      case 'health':
        return isDark ? 'text-red-400 group-hover:text-red-300' : 'text-red-600 group-hover:text-red-500';
      default:
        return isDark ? 'text-gray-400 group-hover:text-gray-300' : 'text-gray-600 group-hover:text-gray-500';
    }
  };

  // Get icon background color based on category
  const getIconBgColor = () => {
    switch(category) {
      case 'finance':
        return isDark ? 'rgba(30, 64, 175, 0.1)' : 'rgba(219, 234, 254, 0.6)';
      case 'math':
        return isDark ? 'rgba(126, 34, 206, 0.1)' : 'rgba(243, 232, 255, 0.6)';
      case 'conversion':
        return isDark ? 'rgba(21, 128, 61, 0.1)' : 'rgba(220, 252, 231, 0.6)';
      case 'health':
        return isDark ? 'rgba(185, 28, 28, 0.1)' : 'rgba(254, 226, 226, 0.6)';
      default:
        return isDark ? 'rgba(75, 85, 99, 0.1)' : 'rgba(243, 244, 246, 0.6)';
    }
  };

  // Get icon hover background color based on category
  const getIconHoverBgColor = () => {
    switch(category) {
      case 'finance':
        return isDark ? 'rgba(30, 64, 175, 0.2)' : 'rgba(191, 219, 254, 0.8)';
      case 'math':
        return isDark ? 'rgba(126, 34, 206, 0.2)' : 'rgba(233, 213, 255, 0.8)';
      case 'conversion':
        return isDark ? 'rgba(21, 128, 61, 0.2)' : 'rgba(187, 247, 208, 0.8)';
      case 'health':
        return isDark ? 'rgba(185, 28, 28, 0.2)' : 'rgba(254, 202, 202, 0.8)';
      default:
        return isDark ? 'rgba(75, 85, 99, 0.2)' : 'rgba(229, 231, 235, 0.8)';
    }
  };

  // Get icon glow color based on category
  const getIconGlowColor = () => {
    switch(category) {
      case 'finance':
        return isDark ? 'rgba(59, 130, 246, 0.5)' : 'rgba(59, 130, 246, 0.3)';
      case 'math':
        return isDark ? 'rgba(168, 85, 247, 0.5)' : 'rgba(168, 85, 247, 0.3)';
      case 'conversion':
        return isDark ? 'rgba(34, 197, 94, 0.5)' : 'rgba(34, 197, 94, 0.3)';
      case 'health':
        return isDark ? 'rgba(239, 68, 68, 0.5)' : 'rgba(239, 68, 68, 0.3)';
      default:
        return isDark ? 'rgba(107, 114, 128, 0.5)' : 'rgba(107, 114, 128, 0.3)';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.3, delay }}
      whileHover={{ y: -5 }}
      className="h-full"
    >
      <Link
        href={`/calculators/${id}`}
        onClick={handleClick}
        className={`group relative block h-full p-6 rounded-xl border transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md ${
          isNavigating ? 'opacity-75' : ''
        }`}
        style={{
          background: isDark
            ? 'var(--bg-secondary)'
            : 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 50%, #ffffff 100%)',
          borderColor: isDark ? '#374151' : '#e5e7eb',
          color: 'var(--text-primary)'
        }}
      >
        <div className="flex items-start justify-between mb-4">
          <div className="relative">
            <motion.div
              className={`text-4xl ${getIconColor()} p-2 rounded-lg transition-all duration-300`}
              style={{
                backgroundColor: getIconBgColor()
              }}
              whileHover={{
                scale: 1.2,
                rotate: 5,
                backgroundColor: getIconHoverBgColor()
              }}
              transition={{ duration: 0.3 }}
            >
              {renderIcon(icon, "w-8 h-8")}
            </motion.div>
            <div
              className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md -z-10"
              style={{
                backgroundColor: getIconGlowColor()
              }}
            />
          </div>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor()}`}>
            {categoryLabels[category].replace(' Calculators', '')}
          </span>
        </div>

        <h3 className="text-xl font-semibold mb-3 group-hover:text-blue-500 transition-colors"
          style={{ color: 'var(--text-primary)' }}
        >
          {title}
        </h3>

        <p className="mb-4" style={{ color: 'var(--text-secondary)' }}>
          {description}
        </p>

        <div className="flex justify-end items-center mt-auto">
          <motion.div
            whileHover={{ x: 4 }}
            transition={{ duration: 0.2 }}
          >
            <FiArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-all" />
          </motion.div>
        </div>

        {/* Gradient background effect on hover */}
        <div className="absolute inset-0 -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          style={{
            background: isDark
              ? 'linear-gradient(to bottom right, rgba(30, 64, 175, 0.2), rgba(31, 41, 55, 0.8))'
              : 'linear-gradient(135deg, #fef3c7 0%, #fbbf24 30%, #f59e0b 70%, #ffffff 100%)'
          }}
        />

        {/* Subtle border glow effect on hover */}
        <div className="absolute inset-0 -z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl rounded-xl"
          style={{
            backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'
          }}
        />
      </Link>
    </motion.div>
  );
}
