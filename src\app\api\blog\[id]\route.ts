import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import mongoose from "mongoose";

// Get a single blog post by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectToDatabase();

    const { id } = params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid post ID format" },
        { status: 400 },
      );
    }

    const post = await BlogPost.findById(id).populate("authorId", "name email");

    if (!post) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 },
      );
    }

    return NextResponse.json(post);
  } catch (error: any) {
    console.error("Error fetching blog post:", error);
    return NextResponse.json(
      { error: "Failed to fetch blog post" },
      { status: 500 },
    );
  }
}

// Update a blog post
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectToDatabase();

    // Get user ID and role from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    const userRole = request.headers.get("x-user-role");

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const data = await request.json();

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid post ID format" },
        { status: 400 },
      );
    }

    // Find the post first to check ownership
    const existingPost = await BlogPost.findById(id);

    if (!existingPost) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 },
      );
    }

    // Only allow the author or admin to update
    if (
      existingPost.authorId.toString() !== userId &&
      userRole !== "admin"
    ) {
      return NextResponse.json(
        { error: "You are not authorized to update this post" },
        { status: 403 },
      );
    }

    // Handle status changes
    if (data.status === "published" && existingPost.status !== "published") {
      data.publishedAt = new Date();
    } else if (data.status === "scheduled" && data.scheduledAt) {
      data.scheduledAt = new Date(data.scheduledAt);
    }

    // Update the post
    const updatedPost = await BlogPost.findByIdAndUpdate(
      id,
      { $set: data },
      { new: true, runValidators: true },
    ).populate("authorId", "name email");

    return NextResponse.json(updatedPost);
  } catch (error: any) {
    console.error("Error updating blog post:", error);

    // Handle duplicate slug error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: "A post with this slug already exists" },
        { status: 400 },
      );
    }

    return NextResponse.json(
      { error: "Failed to update blog post" },
      { status: 500 },
    );
  }
}

// Delete a blog post
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    await connectToDatabase();

    // Get user ID and role from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    const userRole = request.headers.get("x-user-role");

    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid post ID format" },
        { status: 400 },
      );
    }

    // Find the post first to check ownership
    const existingPost = await BlogPost.findById(id);

    if (!existingPost) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 },
      );
    }

    // Only allow the author or admin to delete
    if (
      existingPost.authorId.toString() !== userId &&
      userRole !== "admin"
    ) {
      return NextResponse.json(
        { error: "You are not authorized to delete this post" },
        { status: 403 },
      );
    }

    // Delete the post
    await BlogPost.findByIdAndDelete(id);

    return NextResponse.json(
      { message: "Blog post deleted successfully" },
      { status: 200 },
    );
  } catch (error: any) {
    console.error("Error deleting blog post:", error);
    return NextResponse.json(
      { error: "Failed to delete blog post" },
      { status: 500 },
    );
  }
}
