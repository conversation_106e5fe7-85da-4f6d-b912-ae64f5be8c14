// This is a server component
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { BlogPostContent } from "@/components/blog/BlogPostContent";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";

// Fallback to static data if needed
import { blogPosts } from "@/data/blog-posts";

type Props = {
  params: { slug: string };
};

// Generate metadata for SEO
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const slug = params.slug;

  try {
    // Connect to the database
    await connectToDatabase();

    // Find the blog post by slug
    const post = await BlogPost.findOne({ slug }).populate('authorId', 'name');

    if (!post) {
      // Fallback to static data
      const staticPost = blogPosts[slug as keyof typeof blogPosts];

      if (!staticPost) {
        return {
          title: "Post Not Found - PDF Tools Blog",
          description: "The requested blog post could not be found.",
        };
      }

      return {
        title: `${staticPost.title} - PDF Tools Blog`,
        description: staticPost.description || `Read about ${staticPost.title} on our blog.`,
        keywords: `PDF tools, ${staticPost.category.toLowerCase()}, PDF management, PDF tips`,
        authors: [{ name: staticPost.author }],
        openGraph: {
          title: staticPost.title,
          description: staticPost.description || `Read about ${staticPost.title} on our blog.`,
          images: [
            {
              url: staticPost.image,
              width: 800,
              height: 600,
              alt: staticPost.title,
            },
          ],
          type: "article",
          publishedTime: staticPost.date,
          authors: [staticPost.author],
          tags: [staticPost.category],
        },
        twitter: {
          card: "summary_large_image",
          title: staticPost.title,
          description: staticPost.description || `Read about ${staticPost.title} on our blog.`,
          images: [staticPost.image],
        },
      };
    }

    // Get the author name
    const authorName = post.authorId ? (post.authorId as any).name : 'Admin';

    // Get the category name
    const category = post.category || 'General';

    // Format the date
    const publishedDate = post.publishedAt ? new Date(post.publishedAt).toISOString() : new Date().toISOString();

    return {
      title: `${post.title} - PDF Tools Blog`,
      description: post.excerpt || `Read about ${post.title} on our blog.`,
      keywords: `PDF tools, ${category.toLowerCase()}, PDF management, PDF tips`,
      authors: [{ name: authorName }],
      openGraph: {
        title: post.title,
        description: post.excerpt || `Read about ${post.title} on our blog.`,
        images: [
          {
            url: post.featuredImage || '/blog-placeholder.jpg',
            width: 800,
            height: 600,
            alt: post.title,
          },
        ],
        type: "article",
        publishedTime: publishedDate,
        authors: [authorName],
        tags: [category],
      },
      twitter: {
        card: "summary_large_image",
        title: post.title,
        description: post.excerpt || `Read about ${post.title} on our blog.`,
        images: [post.featuredImage || '/blog-placeholder.jpg'],
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);

    // Fallback to static data
    const staticPost = blogPosts[slug as keyof typeof blogPosts];

    if (!staticPost) {
      return {
        title: "Post Not Found - PDF Tools Blog",
        description: "The requested blog post could not be found.",
      };
    }

    return {
      title: `${staticPost.title} - PDF Tools Blog`,
      description: staticPost.description || `Read about ${staticPost.title} on our blog.`,
      keywords: `PDF tools, ${staticPost.category.toLowerCase()}, PDF management, PDF tips`,
    };
  }
}

// Get the blog post data
async function getBlogPost(slug: string) {
  try {
    // Connect to the database
    await connectToDatabase();

    // Find the blog post by slug
    const post = await BlogPost.findOne({ slug }).populate('authorId', 'name');

    if (!post) {
      // Fallback to static data
      return blogPosts[slug as keyof typeof blogPosts] || null;
    }

    // Get the author name
    const authorName = post.authorId ? (post.authorId as any).name : 'Admin';

    // Format the post data
    return {
      _id: post._id.toString(),
      title: post.title,
      content: post.content,
      date: post.publishedAt ? new Date(post.publishedAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }) : new Date().toLocaleDateString(),
      author: authorName,
      category: post.category || 'General',
      image: post.featuredImage || '/blog-placeholder.jpg',
      description: post.excerpt || '',
    };
  } catch (error) {
    console.error("Error fetching blog post:", error);

    // Fallback to static data
    return blogPosts[slug as keyof typeof blogPosts] || null;
  }
}

export default async function BlogPostPage({ params }: Props) {
  const slug = params.slug;
  const post = await getBlogPost(slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      <Header />
      <BlogPostContent post={post} slug={slug} />
      <Footer />
    </div>
  );
}
