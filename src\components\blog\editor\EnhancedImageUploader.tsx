'use client';

import { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Upload,
  Image as ImageIcon,
  Trash2,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Cloud,
  FolderOpen
} from 'lucide-react';
import Image from 'next/image';
import { toast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import logger from '@/lib/secureLogger';

interface EnhancedImageUploaderProps {
  label: string;
  currentImageUrl?: string;
  onImageUpload: (imageData: {
    url: string;
    alt?: string;
    title?: string;
    width?: number;
    height?: number;
    alignment?: 'left' | 'center' | 'right';
    sizeOption?: string;
  }) => void;
  maxFileSizeMB?: number;
  maxFiles?: number;
  className?: string;
}

export function EnhancedImageUploader({
  label,
  currentImageUrl,
  onImageUpload,
  maxFileSizeMB = 50,
  maxFiles = 5,
  className = '',
}: EnhancedImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const [dragActive, setDragActive] = useState(false);
  const [imageAlt, setImageAlt] = useState('');
  const [imageTitle, setImageTitle] = useState('');
  const [imageWidth, setImageWidth] = useState(100);
  const [imageHeight, setImageHeight] = useState(100);
  const [imageAlignment, setImageAlignment] = useState<'left' | 'center' | 'right'>('center');
  const [imageSizeOption, setImageSizeOption] = useState<string>('M');
  const [showGoogleDriveModal, setShowGoogleDriveModal] = useState(false);
  const [showDropboxModal, setShowDropboxModal] = useState(false);

  // Image size options
  const imageSizeOptions = [
    { name: "ES", label: "Extra Small", width: 150 },
    { name: "S", label: "Small", width: 300 },
    { name: "M", label: "Medium", width: 500 },
    { name: "L", label: "Large", width: 800 },
    { name: "XL", label: "Extra Large", width: 1024 },
    { name: "XXL", label: "XX Large", width: 1280 },
    { name: "custom", label: "Custom Size", width: 0 },
  ];

  const fileInputRef = useRef<HTMLInputElement>(null);
  const maxSizeBytes = maxFileSizeMB * 1024 * 1024;

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, []);

  // Handle file input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();

    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  }, []);

  // Process files
  const handleFiles = useCallback((files: FileList) => {
    // Check if too many files
    if (files.length > maxFiles) {
      toast({
        title: 'Too many files',
        description: `Please upload a maximum of ${maxFiles} files at once.`,
        variant: 'destructive',
      });
      return;
    }

    // Process the first file (we'll handle multiple files in a future update)
    const file = files[0];

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp', 'image/x-icon'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a JPEG, PNG, GIF, SVG, WebP, or ICO file.',
        variant: 'destructive',
      });
      return;
    }

    // Check file size
    if (file.size > maxSizeBytes) {
      toast({
        title: 'File too large',
        description: `Please upload an image smaller than ${maxFileSizeMB}MB.`,
        variant: 'destructive',
      });
      return;
    }

    // Create a preview URL
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    // Set default image title from filename
    const fileName = file.name.split('.')[0];
    setImageTitle(fileName);

    // Upload the file
    uploadFile(file);
  }, [maxFiles, maxSizeBytes, maxFileSizeMB]);

  // Compress image before upload
  const compressImage = async (file: File): Promise<File> => {
    // If file is already small enough, don't compress
    if (file.size <= 1024 * 1024) { // 1MB
      return file;
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (event) => {
        const img = document.createElement('img');
        img.src = event.target?.result as string;

        img.onload = () => {
          const canvas = document.createElement('canvas');
          let width = img.width;
          let height = img.height;

          // Calculate new dimensions while maintaining aspect ratio
          const MAX_WIDTH = 1600;
          const MAX_HEIGHT = 1200;

          if (width > height) {
            if (width > MAX_WIDTH) {
              height = Math.round(height * (MAX_WIDTH / width));
              width = MAX_WIDTH;
            }
          } else {
            if (height > MAX_HEIGHT) {
              width = Math.round(width * (MAX_HEIGHT / height));
              height = MAX_HEIGHT;
            }
          }

          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext('2d');
          ctx?.drawImage(img, 0, 0, width, height);

          // Convert to blob with reduced quality
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                logger.error('Canvas to Blob conversion failed');
                reject(new Error('Canvas to Blob conversion failed'));
                return;
              }

              // Create a new file from the blob
              const newFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now(),
              });

              resolve(newFile);
            },
            'image/jpeg',
            0.8 // Quality (0.8 = 80%)
          );
        };

        img.onerror = () => {
          logger.error('Failed to load image for compression');
          reject(new Error('Failed to load image for compression'));
        };
      };

      reader.onerror = () => {
        logger.error('Failed to read file for compression');
        reject(new Error('Failed to read file for compression'));
      };
    });
  };

  // Memoized callbacks for dialog open state changes
  const handleGoogleDriveModalChange = useCallback((open: boolean) => {
    setShowGoogleDriveModal(open);
  }, []);

  const handleDropboxModalChange = useCallback((open: boolean) => {
    setShowDropboxModal(open);
  }, []);

  // Upload file to server
  const uploadFile = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Compress image before upload
      const compressedFile = await compressImage(file);
      logger.info(`Image compressed for upload`);

      const formData = new FormData();
      formData.append('file', compressedFile);
      formData.append('type', 'image');

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress >= 90 ? 90 : newProgress;
        });
      }, 300);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include', // Include cookies for authentication
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload image');
      }

      const data = await response.json();

      // Wait a moment to show 100% progress
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);

        toast({
          title: 'Image uploaded',
          description: 'Your image has been uploaded successfully.',
        });
      }, 500);

      // Don't call onImageUpload yet - wait for user to configure image settings
      // We'll store the URL for when they click "Insert Image"
      setPreviewUrl(data.fileUrl);
    } catch (error) {
      logger.error('Image upload error');
      setIsUploading(false);
      setUploadProgress(0);

      const errorMessage = error instanceof Error ? error.message : 'Failed to upload image';

      toast({
        title: 'Upload failed',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Handle image insertion
  const handleInsertImage = () => {
    if (previewUrl) {
      // If size option is selected, calculate dimensions based on the option
      let width = imageWidth;
      let height = imageHeight;

      if (imageSizeOption !== 'custom') {
        const sizeOption = imageSizeOptions.find(option => option.name === imageSizeOption);
        if (sizeOption && sizeOption.width > 0) {
          // Calculate height proportionally
          const aspectRatio = imageHeight / imageWidth;
          width = sizeOption.width;
          height = Math.round(width * aspectRatio);
        }
      }

      onImageUpload({
        url: previewUrl,
        alt: imageAlt,
        title: imageTitle,
        width: width,
        height: height,
        alignment: imageAlignment,
        sizeOption: imageSizeOption,
      });
    }
  };

  // Handle remove image
  const handleRemoveImage = () => {
    setPreviewUrl(null);
    setImageAlt('');
    setImageTitle('');
    setImageWidth(100);
    setImageHeight(100);
    setImageAlignment('center');
    setImageSizeOption('M');

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <Label>{label}</Label>

      {/* Drag and drop area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 transition-all ${
          dragActive
            ? 'border-primary bg-primary/5'
            : 'border-[rgb(var(--border))] hover:border-primary/50'
        } ${
          isUploading ? 'opacity-50 pointer-events-none' : ''
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {previewUrl ? (
          <div className="flex flex-col items-center gap-4">
            <div className="relative max-w-full">
              <Image
                src={previewUrl}
                alt={imageAlt || 'Preview'}
                width={400}
                height={300}
                className="object-contain rounded-md max-h-[300px]"
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                }}
              />
            </div>

            {/* Image settings */}
            <div className="w-full space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="image-alt">Alt Text</Label>
                  <Input
                    id="image-alt"
                    value={imageAlt}
                    onChange={(e) => setImageAlt(e.target.value)}
                    placeholder="Describe the image"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="image-title">Title</Label>
                  <Input
                    id="image-title"
                    value={imageTitle}
                    onChange={(e) => setImageTitle(e.target.value)}
                    placeholder="Image title"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Alignment</Label>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant={imageAlignment === 'left' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setImageAlignment('left')}
                    >
                      <AlignLeft className="h-4 w-4 mr-1" />
                      Left
                    </Button>
                    <Button
                      type="button"
                      variant={imageAlignment === 'center' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setImageAlignment('center')}
                    >
                      <AlignCenter className="h-4 w-4 mr-1" />
                      Center
                    </Button>
                    <Button
                      type="button"
                      variant={imageAlignment === 'right' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setImageAlignment('right')}
                    >
                      <AlignRight className="h-4 w-4 mr-1" />
                      Right
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Image Size</Label>
                  <div className="grid grid-cols-3 gap-1">
                    {imageSizeOptions.slice(0, 6).map((option) => (
                      <Button
                        key={option.name}
                        type="button"
                        variant={imageSizeOption === option.name ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setImageSizeOption(option.name)}
                        title={option.label}
                        className="text-xs"
                      >
                        {option.name}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={handleRemoveImage}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Remove
                </Button>
                <Button
                  type="button"
                  onClick={handleInsertImage}
                >
                  Insert Image
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center gap-4">
            <ImageIcon className="h-16 w-16 text-[rgb(var(--muted-foreground))]" />
            <p className="text-center text-[rgb(var(--muted-foreground))]">
              Drag and drop an image here, or click to select
            </p>

            <div className="flex flex-wrap gap-2 justify-center">
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Select File
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => setShowGoogleDriveModal(true)}
                disabled={isUploading}
                className="flex items-center gap-2"
              >
                <Cloud className="h-4 w-4" />
                Google Drive
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => setShowDropboxModal(true)}
                disabled={isUploading}
                className="flex items-center gap-2"
              >
                <FolderOpen className="h-4 w-4" />
                Dropbox
              </Button>
            </div>
          </div>
        )}

        <Input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/gif,image/svg+xml,image/webp,image/x-icon"
          onChange={handleChange}
          className="hidden"
        />
      </div>

      {/* Upload progress */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading...</span>
            <span>{Math.round(uploadProgress)}%</span>
          </div>
          <Progress value={uploadProgress} />
        </div>
      )}

      {/* Google Drive Modal */}
      <Dialog
        open={showGoogleDriveModal}
        onOpenChange={handleGoogleDriveModalChange}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select from Google Drive</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-center text-muted-foreground">
              Google Drive integration will be available soon.
            </p>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dropbox Modal */}
      <Dialog
        open={showDropboxModal}
        onOpenChange={handleDropboxModalChange}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Select from Dropbox</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-center text-muted-foreground">
              Dropbox integration will be available soon.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
