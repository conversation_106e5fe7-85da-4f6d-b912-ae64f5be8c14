"use client";

import Link from "next/link";
import { BlogLayout } from "@/components/blog/BlogLayout";
import { motion } from "framer-motion";
import { Calendar, ArrowRight, Tag, AlertCircle } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { getAllPosts, searchPosts } from "@/services/blogService";
import { useEffect, useState } from "react";


export default function BlogPage() {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get("q") || "";
  const categoryFilter = searchParams.get("category") || "";
  const tagFilter = searchParams.get("tag") || "";

  // Get all posts or filtered posts based on search query
  const [displayedPosts, setDisplayedPosts] = useState(getAllPosts());

  useEffect(() => {
    if (searchQuery) {
      setDisplayedPosts(searchPosts(searchQuery));
    } else if (categoryFilter) {
      setDisplayedPosts(getAllPosts().filter(post =>
        post.category.toLowerCase() === categoryFilter.toLowerCase()
      ));
    } else if (tagFilter) {
      setDisplayedPosts(getAllPosts().filter(post =>
        post.tags?.some(tag => tag.toLowerCase() === tagFilter.toLowerCase())
      ));
    } else {
      setDisplayedPosts(getAllPosts());
    }
  }, [searchQuery, categoryFilter, tagFilter]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 }
    }
  };

  // Determine subtitle based on filters
  let subtitle = "Articles, guides, and tips about PDF management and conversion";
  if (searchQuery) {
    subtitle = `Search results for "${searchQuery}"`;
  } else if (categoryFilter) {
    subtitle = `Articles in ${categoryFilter}`;
  } else if (tagFilter) {
    subtitle = `Articles tagged with "${tagFilter}"`;
  }

  return (
    <BlogLayout title="Blog" subtitle={subtitle}>
      <div className="max-w-5xl mx-auto">
        {displayedPosts.length === 0 ? (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-medium mb-2">No posts found</h3>
            <p className="text-muted-foreground">
              {searchQuery ?
                `No posts match your search for "${searchQuery}"` :
                "No posts available at the moment"}
            </p>
          </motion.div>
        ) : (
          <>
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {displayedPosts.map((post) => (
                <motion.article
                  key={post.id}
                  className="bg-card text-card-foreground rounded-lg overflow-hidden shadow-sm border border-border hover:shadow-md transition-all"
                  variants={itemVariants}
                >
                  <Link href={`/blog/${post.id}`}>
                    <div className="relative h-48 overflow-hidden">
                      <img
                        src={post.image}
                        alt={post.title}
                        className="w-full h-full object-cover transition-transform hover:scale-110 duration-700"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-primary/90 text-primary-foreground rounded-full text-xs font-medium">
                          {post.category}
                        </span>
                      </div>
                    </div>
                  </Link>

                  <div className="p-5">
                    <div className="flex items-center text-muted-foreground text-xs gap-4 mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{post.date}</span>
                      </div>
                    </div>

                    <Link href={`/blog/${post.id}`}>
                      <h2 className="text-xl font-bold mb-2 text-foreground hover:text-primary transition-colors">
                        {post.title}
                      </h2>
                    </Link>

                    <p className="text-muted-foreground mb-4 line-clamp-2">
                      {post.description || ""}
                    </p>

                    <Link
                      href={`/blog/${post.id}`}
                      className="text-primary text-sm font-medium hover:underline inline-flex items-center"
                    >
                      Read More
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                </motion.article>
              ))}
            </motion.div>


          </>
        )}
      </div>
    </BlogLayout>
  );
}
