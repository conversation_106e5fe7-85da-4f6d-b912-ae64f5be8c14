"use client";

import { useCallback, useEffect, useState } from "react";
import { useE<PERSON><PERSON>, EditorContent, BubbleMenu } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import Table from "@tiptap/extension-table";
import TableRow from "@tiptap/extension-table-row";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import Underline from "@tiptap/extension-underline";
import TextAlign from "@tiptap/extension-text-align";
import Placeholder from "@tiptap/extension-placeholder";
import HorizontalRule from "@tiptap/extension-horizontal-rule";
import BulletList from "@tiptap/extension-bullet-list";
import OrderedList from "@tiptap/extension-ordered-list";
import ListItem from "@tiptap/extension-list-item";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";
import FontFamily from "@tiptap/extension-font-family";
import TextStyle from "@tiptap/extension-text-style";
import Color from "@tiptap/extension-color";
import Highlight from "@tiptap/extension-highlight";
import { ResizableImage } from "./extensions/ResizableImageExtension";
import { EnhancedImageUploader } from "./EnhancedImageUploader";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Bold, Italic, LinkIcon } from "lucide-react";

interface TipTapEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  editable?: boolean;
  onEditorReady?: (editor: any) => void;
}

export function TipTapEditor({
  content,
  onChange,
  placeholder = "Write your content here...",
  editable = true,
  onEditorReady,
}: TipTapEditorProps) {
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [linkText, setLinkText] = useState("");
  const [selectedText, setSelectedText] = useState("");
  const [wordCount, setWordCount] = useState({ words: 0, characters: 0 });

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Don't disable these list extensions since we're providing our own
        // bulletList: false,
        // orderedList: false,
        // listItem: false,
      }),
      Underline,
      // Text styling extensions
      TextStyle,
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      // List extensions
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc pl-6',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'list-decimal pl-6',
        },
      }),
      ListItem.configure({
        HTMLAttributes: {
          class: 'my-2',
        },
      }),
      // Task list extensions
      TaskList.configure({
        HTMLAttributes: {
          class: 'task-list',
        },
      }),
      TaskItem.configure({
        HTMLAttributes: {
          class: 'task-item',
        },
        nested: true,
      }),
      // Image extensions
      ResizableImage.configure({
        HTMLAttributes: {
          class: 'blog-image',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'blog-image',
        },
        allowBase64: true,
        inline: true,
      }),
      // Other extensions
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "text-primary underline cursor-pointer",
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableCell,
      TableHeader,
      TextAlign.configure({
        types: ["heading", "paragraph", "image", "resizableImage"],
      }),
      HorizontalRule,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());

      // Update word count
      const text = editor.getText();
      const words = text.split(/\s+/).filter(word => word.length > 0).length;
      const characters = text.length;
      setWordCount({ words, characters });
    },
    editorProps: {
      attributes: {
        class: "prose prose-lg dark:prose-invert focus:outline-none max-w-none min-h-[500px] p-4",
      },
    },
  });

  // Update editor content when content prop changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  // Call onEditorReady when editor is initialized
  useEffect(() => {
    if (editor && onEditorReady) {
      onEditorReady(editor);
    }
  }, [editor, onEditorReady]);

  // Handle link insertion
  const handleLinkDialog = useCallback(() => {
    if (editor) {
      const selection = editor.state.selection;
      const selectedText = selection.empty
        ? ""
        : editor.state.doc.textBetween(selection.from, selection.to);

      setSelectedText(selectedText);
      setLinkText(selectedText);

      const { from, to } = selection;
      const linkMark = editor.state.doc.rangeHasMark(from, to, editor.schema.marks.link);

      if (linkMark) {
        const linkAttrs = editor.getAttributes('link');
        setLinkUrl(linkAttrs.href || "");
      } else {
        setLinkUrl("");
      }

      setIsLinkDialogOpen(true);
    }
  }, [editor]);

  const insertLink = useCallback(() => {
    if (editor) {
      // If there's no selection and we have link text, insert it
      if (selectedText === "" && linkText) {
        editor
          .chain()
          .focus()
          .insertContent(linkText)
          .setTextSelection({
            from: editor.state.selection.from - linkText.length,
            to: editor.state.selection.from
          })
          .setLink({ href: linkUrl })
          .run();
      } else {
        // Otherwise just apply the link to the selection
        editor
          .chain()
          .focus()
          .setLink({ href: linkUrl })
          .run();
      }

      setIsLinkDialogOpen(false);
    }
  }, [editor, linkUrl, linkText, selectedText]);

  const handleLinkDialogOpenChange = useCallback((open: boolean) => {
    setIsLinkDialogOpen(open);
  }, []);

  if (!editor) {
    return <div className="h-64 w-full bg-muted animate-pulse rounded-md" />;
  }

  return (
    <div className="tiptap-editor border border-input rounded-md overflow-hidden">
      <style jsx global>{`
        .task-list {
          list-style-type: none;
          padding-left: 0;
        }
        .task-list li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 0.5em;
        }
        .task-list li > label {
          margin-right: 0.5em;
          user-select: none;
        }
        .task-list li > div {
          flex: 1;
        }
        .task-list input[type="checkbox"] {
          margin-top: 0.2em;
        }
      `}</style>

      {/* Editor Content */}
      <div className="p-4">
        <EditorContent editor={editor} />
      </div>

      {/* Word Count - Fixed at the bottom */}
      <div className="flex justify-between items-center p-3 text-xs text-muted-foreground border-t border-input bg-card sticky bottom-0 z-10">
        <div>
          {wordCount.words} words | {wordCount.characters} characters
        </div>
      </div>

      {/* Link Dialog */}
      <Dialog
        open={isLinkDialogOpen}
        onOpenChange={handleLinkDialogOpenChange}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert Link</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            {selectedText === "" && (
              <div className="space-y-2">
                <Label htmlFor="link-text">Link Text</Label>
                <Input
                  id="link-text"
                  value={linkText}
                  onChange={(e) => setLinkText(e.target.value)}
                  placeholder="Enter link text"
                />
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="link-url">URL</Label>
              <Input
                id="link-url"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="https://example.com"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsLinkDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={insertLink}
                disabled={!linkUrl}
              >
                Insert Link
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Bubble Menu */}
      {editor && (
        <BubbleMenu
          editor={editor}
          tippyOptions={{ duration: 100 }}
          className="bg-background border border-input rounded-md shadow-md p-1 flex items-center gap-1"
        >
          <Button
            type="button"
            size="icon"
            variant={editor.isActive("bold") ? "default" : "ghost"}
            onClick={() => editor.chain().focus().toggleBold().run()}
            className="h-7 w-7"
          >
            <Bold className="h-3.5 w-3.5" />
          </Button>
          <Button
            type="button"
            size="icon"
            variant={editor.isActive("italic") ? "default" : "ghost"}
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className="h-7 w-7"
          >
            <Italic className="h-3.5 w-3.5" />
          </Button>
          <Button
            type="button"
            size="icon"
            variant={editor.isActive("link") ? "default" : "ghost"}
            onClick={handleLinkDialog}
            className="h-7 w-7"
          >
            <LinkIcon className="h-3.5 w-3.5" />
          </Button>
        </BubbleMenu>
      )}
    </div>
  );
}
