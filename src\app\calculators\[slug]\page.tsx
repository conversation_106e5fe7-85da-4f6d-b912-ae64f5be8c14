import { notFound } from "next/navigation";
import { Suspense } from "react";
import { Metadata } from "next";
import { ALL_CALCULATORS } from "@/data/calculators";
import CalculatorLayout from "@/components/calculators/CalculatorLayout";
import CalculatorDialog from "@/components/calculators/CalculatorDialog";
import CalculatorSkeleton from "@/components/calculators/CalculatorSkeleton";

type Props = {
  params: { slug: string };
};

// Generate static paths for all calculators
export async function generateStaticParams() {
  return ALL_CALCULATORS.map((calculator) => ({
    slug: calculator.id,
  }));
}

// Enable ISR with blocking fallback for better performance
export const dynamic = 'force-static';
export const revalidate = 3600; // Revalidate every hour

// Generate metadata for SEO
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const calculator = ALL_CALCULATORS.find(calc => calc.id === params.slug);

  if (!calculator) {
    return {
      title: "Calculator Not Found - ToolCrush",
      description: "The requested calculator could not be found.",
    };
  }

  return {
    title: `${calculator.title} - ToolCrush`,
    description: calculator.description,
    keywords: `calculator, ${params.slug.replace("-", " ")}, online calculator, ${calculator.category}`,
    openGraph: {
      title: `${calculator.title} - ToolCrush`,
      description: calculator.description,
      type: 'website',
      url: `/calculators/${params.slug}`,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${calculator.title} - ToolCrush`,
      description: calculator.description,
    },
  };
}

export default function CalculatorPage({ params }: Props) {
  const calculator = ALL_CALCULATORS.find(calc => calc.id === params.slug);

  if (!calculator) {
    notFound();
  }

  return (
    <CalculatorLayout
      title={calculator.title}
      description={calculator.description}
      iconName={calculator.icon}
    >
      <Suspense fallback={<CalculatorSkeleton />}>
        <CalculatorDialog calculator={calculator} isModal={false} />
      </Suspense>
    </CalculatorLayout>
  );
}
