import { z } from "zod";

export const BlogFormSchema = z.object({
  _id: z.string().optional(),
  title: z.string().min(5, "Title must be at least 5 characters"),
  slug: z.string().min(3, "Slug must be at least 3 characters")
    .regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens"),
  content: z.string().min(50, "Content must be at least 50 characters"),
  excerpt: z.string().max(200, "Excerpt cannot exceed 200 characters").optional(),
  status: z.enum(["draft", "published", "scheduled", "archived"]),
  scheduledAt: z.union([z.date(), z.string(), z.null()]).optional(),
  authorId: z.string(),
  image: z.string().url("Must be a valid URL").optional(),
});

export const BlogPostSchema = BlogFormSchema.extend({
  publishedAt: z.union([z.date(), z.string(), z.null()]).optional(),
  createdAt: z.union([z.date(), z.string()]).optional(),
  updatedAt: z.union([z.date(), z.string()]).optional(),
});
