import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "3");

    // Fetch recent published posts
    const posts = await BlogPost.find({ 
      status: "published" 
    })
      .sort({ publishedAt: -1, createdAt: -1 })
      .limit(limit)
      .populate("authorId", "name email")
      .select("title slug excerpt image publishedAt createdAt authorId");

    // Format posts for frontend
    const formattedPosts = posts.map(post => ({
      id: post._id.toString(),
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt || post.title,
      image: post.image || "/images/blog-placeholder.jpg",
      publishedAt: post.publishedAt || post.createdAt,
      author: {
        name: post.authorId?.name || "Anonymous",
        email: post.authorId?.email || ""
      }
    }));

    return NextResponse.json({
      success: true,
      data: formattedPosts,
      count: formattedPosts.length
    });

  } catch (error) {
    console.error("GET /api/blog/recent error:", error);
    return NextResponse.json(
      { 
        success: false,
        error: "Failed to fetch recent posts",
        data: [],
        count: 0
      },
      { status: 500 }
    );
  }
}
