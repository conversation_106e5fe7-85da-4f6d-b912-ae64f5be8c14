"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState, useEffect, useCallback, useRef } from "react";
import { signOut } from "next-auth/react";

import { Button } from "@/components/ui/button";
import {
  LayoutDashboard,
  FileText,
  PlusCircle,
  List,
  ChevronDown,
  Palette,
  X,
  LogOut,
  Home,
  Settings,
  Hammer,
  Users,
  Activity,
  LineChart,
  TrendingUp
} from "lucide-react";

import { usePermissions } from "@/hooks/usePermissions";

interface NavSubItem {
  href: string;
  icon: React.ReactNode;
  label: string;
  external?: boolean;
}

interface NavItem {
  href?: string;
  icon: React.ReactNode;
  label: string;
  external?: boolean;
  subItems?: NavSubItem[];
}

interface AdminSidebarProps {
  onClose?: () => void;
}

export function AdminSidebar({ onClose }: AdminSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const navigationInProgressRef = useRef(false);
  const {
    isAdmin,
    canViewAnalytics,
    canManageSettings
  } = usePermissions();

  // Auto-open submenu based on current path and keep it open
  useEffect(() => {
    if (pathname.includes('/admin/blog')) {
      setOpenSubmenu('Blog');
    } else if (pathname.includes('/admin/tools')) {
      setOpenSubmenu('Tools');
    } else if (pathname.includes('/admin/features')) {
      setOpenSubmenu('Features');
    }
  }, [pathname]);

  // Enhanced navigation handler for mobile with router integration
  const handleNavigation = useCallback(() => {
    // Only close sidebar on mobile
    if (onClose && window.innerWidth < 1024) {
      console.log('Closing sidebar on navigation');
      // Close immediately to prevent UI flashing
      onClose();
    }
  }, [onClose]);

  // Handle link clicks directly in the component
  const handleLinkClick = useCallback((e: React.MouseEvent) => {
    console.log('Link clicked, handling navigation');
    // Close sidebar on mobile when a link is clicked
    handleNavigation();
  }, [handleNavigation]);

  // Add router event listener to close sidebar on route change
  useEffect(() => {
    // Function to handle route change
    const handleRouteChange = () => {
      console.log('Route changed, closing sidebar if on mobile');
      if (onClose && window.innerWidth < 1024) {
        onClose();
      }
      // Reset navigation flag
      navigationInProgressRef.current = false;
    };

    // Add event listener for route changes
    window.addEventListener('popstate', handleRouteChange);

    // Cleanup
    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, [onClose]);

  // Custom navigation handler that uses the router
  const navigateTo = useCallback((href: string, e: React.MouseEvent) => {
    e.preventDefault();

    // Prevent multiple navigations
    if (navigationInProgressRef.current) {
      return;
    }

    // Set navigation in progress
    navigationInProgressRef.current = true;
    console.log(`Navigating to: ${href}`);

    // Close sidebar on mobile immediately
    if (onClose && window.innerWidth < 1024) {
      onClose();
    }

    // Use a small timeout to prevent potential race conditions
    setTimeout(() => {
      // Navigate using the router
      router.push(href);

      // Reset navigation flag after navigation
      setTimeout(() => {
        navigationInProgressRef.current = false;
      }, 500);
    }, 10);
  }, [router, onClose]);

  const getNavItems = (): NavItem[] => {
    const items: NavItem[] = [];

    // Dashboard
    items.push({
      href: "/admin",
      icon: <LayoutDashboard className="h-5 w-5" />,
      label: "Dashboard",
    });

    // Analytics
    if (isAdmin) {
      items.push({
        href: "/admin/analytics",
        icon: <LineChart className="h-5 w-5" />,
        label: "Analytics",
      });
    }

    // Blog
    items.push({
      icon: <FileText className="h-5 w-5" />,
      label: "Blog",
      subItems: [
        {
          href: "/admin/blog/editor",
          icon: <PlusCircle className="h-4 w-4" />,
          label: "New Post",
        },
        {
          href: "/admin/blog/posts",
          icon: <List className="h-4 w-4" />,
          label: "All Posts",
        },
      ],
    });

    // Tools (Admins only)
    if (isAdmin) {
      items.push({
        icon: <Hammer className="h-5 w-5" />,
        label: "Tools",
        subItems: [
          {
            href: "/admin/tools/list",
            icon: <List className="h-4 w-4" />,
            label: "All Tools",
          },
          {
            href: "/admin/tools/stats",
            icon: <Activity className="h-4 w-4" />,
            label: "Tools Usage",
          },
        ],
      });
    }

    // Users (Admins only)
    if (isAdmin) {
      items.push({
        href: "/admin/users",
        icon: <Users className="h-5 w-5" />,
        label: "Users",
      });
    }

    // SEO Settings (Admins only)
    if (isAdmin) {
      items.push({
        href: "/admin/seo",
        icon: <Settings className="h-5 w-5" />,
        label: "SEO Settings",
      });
    }

    return items;
  };

  const navItems = getNavItems();

  const toggleSubmenu = (label: string, e: React.MouseEvent) => {
    // Prevent the event from bubbling up to parent elements
    e.stopPropagation();

    // Toggle the submenu
    setOpenSubmenu(openSubmenu === label ? null : label);
  };

  const handleLogout = async () => {
    // Close sidebar on mobile before logout
    if (onClose && window.innerWidth < 1024) {
      onClose();
    }
    await signOut({ callbackUrl: "/login" });
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <Link href="/admin/blog/posts" className="flex items-center gap-2">
          <div className="bg-primary text-primary-foreground p-1.5 rounded">
            <FileText className="h-5 w-5" />
          </div>
          <span className="font-bold text-xl">Admin Panel</span>
        </Link>
        {onClose && (
          <Button variant="ghost" size="icon" onClick={onClose} className="lg:hidden">
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>

      <div className="flex-1 py-2 overflow-y-auto">
        <nav className="px-2 space-y-1">
          {navItems.map((item) => (
            <div key={item.label}>
              {item.subItems ? (
                <div className="space-y-1">
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-2 text-muted-foreground hover:text-foreground hover:bg-muted/50"
                    onClick={(e) => toggleSubmenu(item.label, e)}
                    aria-expanded={openSubmenu === item.label}
                    aria-controls={`submenu-${item.label}`}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                    <ChevronDown
                      className={`ml-auto h-4 w-4 transition-transform ${
                        openSubmenu === item.label ? "rotate-180" : ""
                      }`}
                    />
                  </Button>

                  <div
                    id={`submenu-${item.label}`}
                    className={`overflow-hidden transition-all duration-300 ease-in-out ${
                      openSubmenu === item.label ? "max-h-96 opacity-100" : "max-h-0 opacity-0 pointer-events-none"
                    }`}
                  >
                    <div className="ml-4 pl-2 border-l space-y-1 py-1">
                      {item.subItems.map((subItem) => (
                        <Button
                          key={subItem.href}
                          variant="ghost"
                          asChild
                          className={`w-full justify-start gap-2 text-muted-foreground hover:text-foreground hover:bg-muted/50 ${
                            (pathname === subItem.href || pathname.startsWith(subItem.href + '/')) &&
                            "bg-primary/10 text-primary font-medium"
                          }`}
                        >
                          {subItem.external ? (
                            <a
                              href={subItem.href}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-2"
                              onClick={handleLinkClick}
                            >
                              {subItem.icon}
                              <span>{subItem.label}</span>
                            </a>
                          ) : (
                            <a
                              href={subItem.href}
                              className="flex items-center gap-2"
                              onClick={(e) => navigateTo(subItem.href, e)}
                            >
                              {subItem.icon}
                              <span>{subItem.label}</span>
                            </a>
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <Button
                  variant="ghost"
                  asChild
                  className={`w-full justify-start gap-2 text-muted-foreground hover:text-foreground hover:bg-muted/50 ${
                    (pathname === item.href || (item.href !== '/' && pathname.startsWith(item.href + '/'))) &&
                    "bg-primary/10 text-primary font-medium"
                  }`}
                >
                  {item.external ? (
                    <a
                      href={item.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2"
                      onClick={handleLinkClick}
                    >
                      {item.icon}
                      <span>{item.label}</span>
                    </a>
                  ) : (
                    <a
                      href={item.href || "#"}
                      className="flex items-center gap-2"
                      onClick={(e) => navigateTo(item.href || "#", e)}
                    >
                      {item.icon}
                      <span>{item.label}</span>
                    </a>
                  )}
                </Button>
              )}
            </div>
          ))}
        </nav>
      </div>

      <div className="p-4 border-t">
        <a
          href="/"
          className="block mb-2"
          onClick={(e) => navigateTo("/", e)}
        >
          <Button
            variant="outline"
            className="w-full justify-start gap-2"
          >
            <Home className="h-5 w-5" />
            <span>Back to Site</span>
          </Button>
        </a>

        <Button
          variant="ghost"
          className="w-full justify-start gap-2 text-muted-foreground hover:text-foreground"
          onClick={handleLogout}
        >
          <LogOut className="h-5 w-5" />
          <span>Logout</span>
        </Button>
      </div>
    </div>
  );
}
