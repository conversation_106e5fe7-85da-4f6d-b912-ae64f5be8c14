// app/admin/blog/page.tsx (or wherever your blog management page is)
"use client";

import { useState } from "react";
import DashboardLayout from "@/components/admin/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { BlogManager } from "@/components/admin/BlogManager";
import { PlusCircle, Search } from "lucide-react";
import { useRouter } from "next/navigation";

export default function BlogManagementPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <CardTitle>All Posts</CardTitle>
                <CardDescription>Manage your blog content</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-adaptive-muted" />
                  <Input
                    type="search"
                    placeholder="Search posts..."
                    className="pl-8 w-[200px]"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button onClick={() => router.push("/admin/blog/editor")}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  New Post
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <BlogManager searchFilter={searchTerm} />
          </CardContent>
        </Card>
      </motion.div>
    </DashboardLayout>
  );
}





