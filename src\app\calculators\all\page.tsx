'use client';

import Link from "next/link";
import { useTheme } from "@/hooks/useTheme";
import ToolsHeader from "@/components/tools/ToolsHeader";
import { motion } from "framer-motion";
import { FiArrowRight, FiSearch, FiTool, FiFileText, FiDownload } from "react-icons/fi";
import { useState, useEffect } from "react";
import { ALL_CALCULATORS, calculatorIcons, categoryLabels } from "@/data/calculators";
import * as LucideIcons from "lucide-react";
import UnifiedCard from "@/components/ui/UnifiedCard";

export default function AllCalculatorsPage() {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredCalculators, setFilteredCalculators] = useState<any[]>([]);
  const [filteredComingSoonCalculators, setFilteredComingSoonCalculators] = useState<any[]>([]);
  const [totalCalculatorCount, setTotalCalculatorCount] = useState(0);

  // Define calculator types for better type safety
  interface Calculator {
    id: string;
    title: string;
    description: string;
    icon: string;
    category: 'finance' | 'math' | 'conversion' | 'health';
    popular: boolean;
    comingSoon?: boolean;
  }

  // Separate regular calculators from coming soon calculators
  const calculators = ALL_CALCULATORS.filter(calc => !calc.comingSoon);
  const comingSoonCalculators = ALL_CALCULATORS.filter(calc => calc.comingSoon);

  // Initialize filtered calculators and calculate total calculator count
  useEffect(() => {
    // Initialize filtered calculators with all calculators
    setFilteredCalculators(calculators);
    setFilteredComingSoonCalculators(comingSoonCalculators);

    // Set the total count of all calculators (available + coming soon)
    setTotalCalculatorCount(calculators.length + comingSoonCalculators.length);
  }, [calculators, comingSoonCalculators]);

  // Filter calculators based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCalculators(calculators);
      setFilteredComingSoonCalculators(comingSoonCalculators);
    } else {
      const query = searchQuery.toLowerCase().trim();

      // Filter available calculators
      const filtered = calculators.filter(calculator =>
        calculator.title.toLowerCase().includes(query) ||
        calculator.description.toLowerCase().includes(query)
      );
      setFilteredCalculators(filtered);

      // Filter coming soon calculators
      const filteredComingSoon = comingSoonCalculators.filter(calculator =>
        calculator.title.toLowerCase().includes(query) ||
        calculator.description.toLowerCase().includes(query)
      );
      setFilteredComingSoonCalculators(filteredComingSoon);
    }
  }, [searchQuery, calculators, comingSoonCalculators]);

  // Function to render the appropriate icon based on the icon name
  const renderIcon = (iconName: string, className: string = "w-6 h-6") => {
    // Convert kebab-case to PascalCase for Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    // Get the icon component from Lucide
    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    // Fallback to a default icon if the specified icon doesn't exist
    return <LucideIcons.Calculator className={className} />;
  };

  return (
    <main className="min-h-screen bg-var-bg-primary text-var-text-primary transition-colors duration-300" style={{
      backgroundColor: 'var(--bg-primary)',
      color: 'var(--text-primary)'
    }}>
      <ToolsHeader />

      {/* Modern Hero Section */}
      <section className="relative overflow-hidden transition-colors duration-300" style={{
        backgroundColor: 'var(--bg-primary)'
      }}>
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/5 dark:from-blue-900/20 dark:to-purple-900/10" />

        {/* Animated background shapes */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          <motion.div
            className="absolute top-20 left-10 w-64 h-64 rounded-full bg-blue-400 dark:bg-blue-600 blur-3xl"
            animate={{
              x: [0, 30, 0],
              y: [0, 20, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              repeat: Infinity,
              duration: 8,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-20 right-10 w-72 h-72 rounded-full bg-purple-400 dark:bg-purple-600 blur-3xl"
            animate={{
              x: [0, -30, 0],
              y: [0, -20, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              repeat: Infinity,
              duration: 10,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="container mx-auto px-4 py-20 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
                All Calculators
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-gray-700 dark:text-gray-300">
                Free online calculators for finance, math, health, and more
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap justify-center gap-4 mb-12"
            >
              <div className="flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                <FiTool className="mr-2" /> {totalCalculatorCount} Calculators
              </div>
              <div className="flex items-center px-4 py-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300">
                <FiFileText className="mr-2" /> Easy to Use
              </div>
              <div className="flex items-center px-4 py-2 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300">
                <FiDownload className="mr-2" /> Instant Results
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="relative max-w-2xl mx-auto mb-8"
            >
              <div className="flex items-center p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700">
                <FiSearch className="ml-3 mr-2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search for a calculator..."
                  className="w-full py-2 px-3 bg-transparent border-none focus:outline-none text-gray-800 dark:text-gray-200"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button
                  className="ml-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full transition-all duration-200 transform hover:scale-105 active:scale-95"
                  onClick={() => {
                    // Scroll to results section when search button is clicked
                    document.getElementById('calculators-section')?.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start'
                    });
                  }}
                >
                  Search
                </button>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Wave divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
            <path
              fill="currentColor"
              fillOpacity="1"
              className="text-gray-50 dark:text-gray-800"
              d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"
            ></path>
          </svg>
        </div>
      </section>

      <section id="calculators-section" className="py-16 transition-colors duration-300" style={{
        backgroundColor: 'var(--bg-secondary)'
      }}>
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold mb-12 text-center"
            style={{ color: 'var(--text-primary)' }}
          >
            Our Calculators Collection
          </motion.h2>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-8 text-center"
          >
            {searchQuery.trim() !== '' ? (
              <div className="flex flex-col items-center">
                <p
                  className="text-lg font-medium mb-2"
                  style={{ color: 'var(--text-primary)' }}
                >
                  {filteredCalculators.length === 0 && filteredComingSoonCalculators.length === 0 ? (
                    'No calculators found matching your search.'
                  ) : (
                    <>
                      <span className="text-blue-600 dark:text-blue-400 font-semibold">
                        {filteredCalculators.length + filteredComingSoonCalculators.length}
                      </span> calculator{filteredCalculators.length + filteredComingSoonCalculators.length === 1 ? '' : 's'} found matching "{searchQuery}"
                    </>
                  )}
                </p>
                {filteredCalculators.length > 0 && (
                  <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                    {filteredCalculators.length} available now • {filteredComingSoonCalculators.length} coming soon
                  </p>
                )}
              </div>
            ) : (
              <p className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>
                Browse our collection of <span className="text-blue-600 dark:text-blue-400 font-semibold">{calculators.length}</span> calculators
              </p>
            )}
          </motion.div>

          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.05,
                  delayChildren: 0.1
                }
              }
            }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {filteredCalculators.map((calculator, index) => (
              <UnifiedCard
                key={calculator.id}
                id={calculator.id}
                title={calculator.title}
                description={calculator.description}
                icon={calculator.icon}
                type="calculator"
                category={calculator.category}
                popular={calculator.popular}
                index={index}
                variant="enhanced"
              />
            ))}
          </motion.div>

          {filteredComingSoonCalculators.length > 0 && (
            <>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="relative mt-20 mb-12"
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-full border-t border-gray-200 dark:border-gray-700"></div>
                </div>
                <div className="relative flex justify-center">
                  <span className="bg-gray-50 dark:bg-gray-800 px-6 py-2 text-lg font-semibold rounded-full"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      color: 'var(--text-primary)'
                    }}
                  >
                    Coming Soon
                  </span>
                </div>
              </motion.div>

              <motion.div
                initial="hidden"
                animate="visible"
                variants={{
                  hidden: { opacity: 0 },
                  visible: {
                    opacity: 1,
                    transition: {
                      staggerChildren: 0.05,
                      delayChildren: 0.4
                    }
                  }
                }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              >
                {filteredComingSoonCalculators.map((calculator, index) => (
                  <UnifiedCard
                    key={calculator.id}
                    id={calculator.id}
                    title={calculator.title}
                    description={calculator.description}
                    icon={calculator.icon}
                    type="calculator"
                    category={calculator.category}
                    popular={calculator.popular}
                    comingSoon={true}
                    index={index}
                    delay={0.05 * (index % 8) + 0.4}
                    variant="enhanced"
                  />
                ))}
              </motion.div>
            </>
          )}
        </div>
      </section>

      <section className="py-16 transition-colors duration-300 relative overflow-hidden" style={{
        backgroundColor: 'var(--bg-primary)'
      }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden opacity-10">
          <motion.div
            className="absolute top-40 left-20 w-72 h-72 rounded-full bg-blue-400 dark:bg-blue-600 blur-3xl"
            animate={{
              x: [0, 30, 0],
              y: [0, 20, 0],
              opacity: [0.2, 0.3, 0.2]
            }}
            transition={{
              repeat: Infinity,
              duration: 12,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-40 right-20 w-80 h-80 rounded-full bg-purple-400 dark:bg-purple-600 blur-3xl"
            animate={{
              x: [0, -30, 0],
              y: [0, -20, 0],
              opacity: [0.2, 0.3, 0.2]
            }}
            transition={{
              repeat: Infinity,
              duration: 15,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6 }}
            className="max-w-2xl mx-auto text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
              Latest from Our Blog
            </h2>
            <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>
              Discover tips, tutorials, and insights about using our calculators effectively
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.1,
                  delayChildren: 0.2
                }
              }
            }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            {[
              {
                image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&q=80",
                alt: "Financial Calculator",
                title: "5 Financial Calculators Everyone Should Use",
                description: "Discover the essential financial calculators that can help you make better money decisions.",
                link: "/blog/essential-financial-calculators",
                category: "Finance"
              },
              {
                image: "https://images.unsplash.com/photo-**********-22dec7ec8818?w=800&q=80",
                alt: "Health Calculator",
                title: "How Health Calculators Can Improve Your Wellness",
                description: "Learn how health calculators can help you track and improve your overall wellness.",
                link: "/blog/health-calculators-wellness",
                category: "Health"
              },
              {
                image: "https://images.unsplash.com/photo-**********-efe14ef6055d?w=800&q=80",
                alt: "Math Calculator",
                title: "Advanced Math Calculators for Students",
                description: "Explore powerful math calculators that can help students solve complex problems.",
                link: "/blog/math-calculators-for-students",
                category: "Education"
              }
            ].map((blog, index) => (
              <motion.div
                key={blog.title}
                variants={{
                  hidden: { opacity: 0, y: 30 },
                  visible: {
                    opacity: 1,
                    y: 0,
                    transition: {
                      duration: 0.5,
                      ease: [0.25, 0.1, 0.25, 1.0]
                    }
                  }
                }}
                whileHover={{
                  y: -10,
                  transition: { duration: 0.3 }
                }}
                className="rounded-xl border overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300"
                style={{
                  backgroundColor: theme === 'dark' ? 'var(--bg-secondary)' : 'white',
                  borderColor: theme === 'dark' ? '#374151' : '#e5e7eb'
                }}
              >
                <div className="relative overflow-hidden">
                  <motion.div
                    whileHover={{ scale: 1.08 }}
                    transition={{ duration: 0.6 }}
                    className="h-48"
                  >
                    <img
                      src={blog.image}
                      alt={blog.alt}
                      className="w-full h-full object-cover"
                    />
                  </motion.div>
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 text-xs font-medium rounded-full"
                      style={{
                        backgroundColor: theme === 'dark' ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.8)',
                        color: theme === 'dark' ? '#94a3b8' : '#475569',
                        backdropFilter: 'blur(4px)'
                      }}
                    >
                      {blog.category}
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-blue-500 transition-colors"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    {blog.title}
                  </h3>
                  <p className="mb-4" style={{ color: 'var(--text-secondary)' }}>
                    {blog.description}
                  </p>
                  <motion.div
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center"
                  >
                    <Link
                      href={blog.link}
                      className="font-medium inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                    >
                      Read Article <FiArrowRight className="ml-2" />
                    </Link>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.2 }}
            >
              <Link
                href="/blog"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-full font-medium transition-all duration-300 inline-flex items-center shadow-md hover:shadow-xl"
              >
                View All Articles
                <FiArrowRight className="ml-2" />
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      <footer className="pt-20 pb-10 relative overflow-hidden" style={{
        backgroundColor: theme === 'dark' ? '#0f172a' : '#1e293b', // dark blue in dark mode, slightly lighter in light mode
        color: 'white'
      }}>
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden opacity-5">
          <svg className="absolute -bottom-1/2 left-0 w-full h-full text-blue-500 opacity-10" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
            <path d="M 0 1000 Q 250 750 500 1000 Q 750 750 1000 1000 L 1000 0 L 0 0 L 0 1000" fill="currentColor" />
          </svg>
          <svg className="absolute -top-1/2 right-0 w-full h-full text-purple-500 opacity-10 transform rotate-180" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
            <path d="M 0 1000 Q 250 750 500 1000 Q 750 750 1000 1000 L 1000 0 L 0 0 L 0 1000" fill="currentColor" />
          </svg>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div className="col-span-1 md:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <h3 className="text-2xl font-bold mb-6 text-white">
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400">
                    Online Calculators
                  </span>
                </h3>
                <p className="text-gray-300 mb-6 max-w-md">
                  Your all-in-one solution for online calculations. We provide powerful tools to help you solve complex problems efficiently.
                </p>
                <div className="flex space-x-4">
                  {[
                    { icon: "M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z", name: "Facebook" },
                    { icon: "M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84", name: "Twitter" },
                    { icon: "M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z", name: "Instagram" },
                  ].map((social, index) => (
                    <motion.a
                      key={social.name}
                      href="#"
                      whileHover={{ y: -5, scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: 0.1 * index }}
                      className="bg-gray-800 hover:bg-gray-700 p-3 rounded-full text-gray-300 hover:text-white transition-all duration-300 transform"
                    >
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d={social.icon} clipRule="evenodd"></path>
                      </svg>
                    </motion.a>
                  ))}
                </div>
              </motion.div>
            </div>
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <h3 className="text-lg font-semibold mb-6 text-white">Quick Links</h3>
                <ul className="space-y-4">
                  {[
                    { href: "/", label: "Home" },
                    { href: "/calculators", label: "Calculator Categories" },
                    { href: "/calculators/all", label: "All Calculators" },
                    { href: "/tools", label: "All Tools" },
                    { href: "/blog", label: "Blog" },
                  ].map((link, index) => (
                    <motion.li
                      key={link.href}
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: 0.05 * index + 0.2 }}
                    >
                      <Link
                        href={link.href}
                        className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center group"
                      >
                        <motion.span
                          className="mr-2 h-4 w-4 text-blue-400 group-hover:text-blue-300"
                          whileHover={{ x: 2 }}
                          transition={{ duration: 0.2 }}
                        >
                          <FiArrowRight />
                        </motion.span>
                        {link.label}
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>
            </div>
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <h3 className="text-lg font-semibold mb-6 text-white">Legal</h3>
                <ul className="space-y-4">
                  {[
                    { href: "/privacy", label: "Privacy Policy" },
                    { href: "/terms", label: "Terms of Service" },
                    { href: "/cookies", label: "Cookie Policy" },
                    { href: "/contact", label: "Contact Us" },
                  ].map((link, index) => (
                    <motion.li
                      key={link.href}
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: 0.05 * index + 0.3 }}
                    >
                      <Link
                        href={link.href}
                        className="text-gray-300 hover:text-white transition-colors duration-200 flex items-center group"
                      >
                        <motion.span
                          className="mr-2 h-4 w-4 text-blue-400 group-hover:text-blue-300"
                          whileHover={{ x: 2 }}
                          transition={{ duration: 0.2 }}
                        >
                          <FiArrowRight />
                        </motion.span>
                        {link.label}
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="border-t border-gray-700/50 mt-16 pt-8 text-center"
          >
            <p className="text-gray-400">
              &copy; {new Date().getFullYear()} Online Calculators. All rights reserved.
            </p>
          </motion.div>
        </div>
      </footer>
    </main>
  );
}