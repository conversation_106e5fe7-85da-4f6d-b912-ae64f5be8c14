import { Metadata } from "next";
import { ALL_CALCULATORS } from "@/data/calculators";

type Props = {
  params: { slug: string };
  children: React.ReactNode;
};

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const calculator = ALL_CALCULATORS.find(calc => calc.id === params.slug);

  if (!calculator) {
    return {
      title: "Calculator Not Found - PDF Tools",
    };
  }

  return {
    title: `${calculator.title} - PDF Tools`,
    description: calculator.description,
    keywords: `calculator, ${params.slug.replace("-", " ")}, online calculator`,
  };
}

export default function CalculatorDetailLayout({ children }: Props) {
  return <>{children}</>;
}
