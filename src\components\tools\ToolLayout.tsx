"use client";

import Link from "next/link";
import { ReactNode } from "react";
import { FiHome, FiTool, FiBook, FiLogIn, FiUserPlus, FiUser, FiSettings } from "react-icons/fi";
import { useAppSelector } from "@/redux/hooks";
import { useSession } from "next-auth/react";

interface ToolLayoutProps {
  children: ReactNode;
  title: string;
  description: string;
  icon: string;
  inputFormat?: string;
  outputFormat?: string;
}

// Common links for all users
const COMMON_LINKS = [
  { href: "/", label: "Home", icon: <FiHome /> },
  { href: "/tools", label: "All Tools", icon: <FiTool /> },
  { href: "/blog", label: "Blog", icon: <FiBook /> },
];

// Links for authenticated users
const AUTH_LINKS = [
  { href: "/dashboard", label: "Dashboard", icon: <FiUser /> },
];

// Links for admin users
const ADMIN_LINKS = [
  { href: "/admin", label: "Admin", icon: <FiSettings /> },
];

// Links for non-authenticated users
const GUEST_LINKS = [
  { href: "/login", label: "Login", icon: <FiLogIn /> },
  { href: "/signup", label: "Sign Up", icon: <FiUserPlus /> },
];

export default function ToolLayout({
  children,
  title,
  description,
  icon,
  inputFormat,
  outputFormat,
}: ToolLayoutProps) {
  // Get authentication status from NextAuth
  const { data: session } = useSession();

  // Get authentication status from Redux
  const { user: reduxUser, isAuthenticated: reduxIsAuthenticated } = useAppSelector((state) => state.auth);

  // Use either NextAuth or Redux for authentication status
  const user = session?.user || reduxUser;
  const isAuthenticated = !!session?.user || reduxIsAuthenticated;
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 flex flex-col md:flex-row justify-between items-center gap-3">
          <Link
            href="/"
            className="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors flex items-center gap-2"
          >
            <FiTool className="w-6 h-6" />
            <span>PDF Tools</span>
          </Link>

          <nav className="w-full md:w-auto">
            <ul className="flex flex-wrap justify-center gap-2 md:gap-4">
              {/* Common links for all users */}
              {COMMON_LINKS.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="flex items-center gap-1 text-gray-700 hover:text-blue-600 transition-colors px-3 py-2 rounded hover:bg-gray-100 text-sm md:text-base"
                  >
                    <span className="text-lg">{link.icon}</span>
                    <span>{link.label}</span>
                  </Link>
                </li>
              ))}

              {/* Conditional links based on authentication status */}
              {isAuthenticated ? (
                <>
                  {/* Links for authenticated users */}
                  {AUTH_LINKS.map((link) => (
                    <li key={link.href}>
                      <Link
                        href={link.href}
                        className="flex items-center gap-1 text-gray-700 hover:text-blue-600 transition-colors px-3 py-2 rounded hover:bg-gray-100 text-sm md:text-base"
                      >
                        <span className="text-lg">{link.icon}</span>
                        <span>{link.label}</span>
                      </Link>
                    </li>
                  ))}

                  {/* Admin links if user has admin role */}
                  {user?.role === 'admin' && ADMIN_LINKS.map((link) => (
                    <li key={link.href}>
                      <Link
                        href={link.href}
                        className="flex items-center gap-1 text-gray-700 hover:text-blue-600 transition-colors px-3 py-2 rounded hover:bg-gray-100 text-sm md:text-base"
                      >
                        <span className="text-lg">{link.icon}</span>
                        <span>{link.label}</span>
                      </Link>
                    </li>
                  ))}
                </>
              ) : (
                <>
                  {/* Links for guests */}
                  {GUEST_LINKS.map((link) => (
                    <li key={link.href}>
                      <Link
                        href={link.href}
                        className="flex items-center gap-1 text-gray-700 hover:text-blue-600 transition-colors px-3 py-2 rounded hover:bg-gray-100 text-sm md:text-base"
                      >
                        <span className="text-lg">{link.icon}</span>
                        <span>{link.label}</span>
                      </Link>
                    </li>
                  ))}
                </>
              )}
            </ul>
          </nav>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="flex items-start gap-4 mb-6 p-4 bg-white rounded-lg shadow-sm">
          <span className="text-4xl" aria-hidden="true">
            {icon}
          </span>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            <p className="text-gray-600 mt-1">{description}</p>
            {inputFormat && outputFormat && (
              <div className="mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {inputFormat} → {outputFormat}
              </div>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {children}
        </div>
      </main>

      <footer className="bg-gray-800 text-white py-8 mt-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h2 className="text-lg font-semibold mb-3">PDF Tools</h2>
              <p className="text-gray-400">Your all-in-one solution for PDF management and conversion.</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Quick Links</h3>
              <ul className="space-y-2">
                {COMMON_LINKS.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors flex items-center gap-2"
                    >
                      <span className="text-sm">{link.icon}</span>
                      <span>{link.label}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/privacy"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href="/terms"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-6 text-center text-sm text-gray-400">
            <p>&copy; {new Date().getFullYear()} PDF Tools. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}