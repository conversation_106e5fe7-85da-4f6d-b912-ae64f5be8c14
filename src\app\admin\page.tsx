"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { RequireRole } from "@/components/auth/RequireRole";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  FiUsers,
  FiFileText,
  FiTool,
  FiSettings,
  FiMessageSquare,
  FiActivity,
  FiArrowRight,
  FiPlusCircle
} from "react-icons/fi";

// Dashboard loading skeleton
function AdminDashboardSkeleton() {
  return (
    <div className="min-h-screen bg-gray-100 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header skeleton */}
        <div className="bg-white rounded-lg shadow p-6 mb-6 animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/5"></div>
        </div>

        {/* Stats skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))}
        </div>

        {/* Content skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center">
                  <div className="h-10 w-10 bg-gray-200 rounded-full mr-3"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center">
                  <div className="h-10 w-10 bg-gray-200 rounded-full mr-3"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Stats type
interface DashboardStat {
  id: string;
  title: string;
  value: number;
  change: number;
  icon: React.ReactNode;
  color: string;
}

// Recent activity type
interface RecentActivity {
  id: string;
  type: "user" | "blog" | "tool" | "comment";
  title: string;
  description: string;
  time: string;
}

export default function AdminDashboard() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<DashboardStat[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch dashboard data
  useEffect(() => {
    async function fetchDashboardData() {
      try {
        // Fetch summary data from analytics API
        const summaryResponse = await fetch('/api/analytics?type=summary&range=month');
        if (!summaryResponse.ok) {
          throw new Error(`Failed to fetch summary data: ${summaryResponse.status}`);
        }
        const summaryData = await summaryResponse.json();

        // Set stats based on API response
        setStats([
          {
            id: "users",
            title: "Total Users",
            value: summaryData.summary?.totalUsers || 0,
            change: summaryData.summary?.userGrowth || 0,
            icon: <FiUsers className="h-6 w-6" />,
            color: "blue"
          },
          {
            id: "blog",
            title: "Blog Posts",
            value: summaryData.summary?.totalPosts || 0,
            change: summaryData.summary?.postGrowth || 0,
            icon: <FiFileText className="h-6 w-6" />,
            color: "green"
          },
          {
            id: "tools",
            title: "Tool Usage",
            value: summaryData.summary?.totalViews || 0,
            change: summaryData.summary?.viewGrowth || 0,
            icon: <FiTool className="h-6 w-6" />,
            color: "purple"
          }
        ]);

        // Fetch recent activities
        // For now, we'll use mock data for activities since we don't have a specific API for this yet
        setRecentActivities([
          {
            id: "1",
            type: "user",
            title: "New User Registration",
            description: "<EMAIL> registered an account",
            time: "10 minutes ago"
          },
          {
            id: "2",
            type: "blog",
            title: "Blog Post Published",
            description: "How to Convert PDF to Word",
            time: "2 hours ago"
          },
          {
            id: "3",
            type: "tool",
            title: "Tool Usage Spike",
            description: "PDF to Excel converter usage increased by 25%",
            time: "Yesterday"
          },
          {
            id: "4",
            type: "comment",
            title: "New Comment",
            description: "New comment on 'PDF Security Tips'",
            time: "2 days ago"
          }
        ]);

        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        setIsLoading(false);
      }
    }

    fetchDashboardData();
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Get icon for activity type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "user":
        return <FiUsers className="h-5 w-5" />;
      case "blog":
        return <FiFileText className="h-5 w-5" />;
      case "tool":
        return <FiTool className="h-5 w-5" />;
      case "comment":
        return <FiMessageSquare className="h-5 w-5" />;
      default:
        return <FiActivity className="h-5 w-5" />;
    }
  };

  // Get color for activity type
  const getActivityColor = (type: string) => {
    switch (type) {
      case "user":
        return "bg-blue-100 text-blue-600";
      case "blog":
        return "bg-green-100 text-green-600";
      case "tool":
        return "bg-purple-100 text-purple-600";
      case "comment":
        return "bg-yellow-100 text-yellow-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  return (
    <RequireRole
      role="admin"
      loadingComponent={<AdminDashboardSkeleton />}
    >
      {isLoading ? (
        <AdminDashboardSkeleton />
      ) : (
        <div className="min-h-screen bg-gray-100">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-6"
            >
              {/* Header */}
              <motion.div
                variants={itemVariants}
                className="bg-white overflow-hidden shadow rounded-lg"
              >
                <div className="px-6 py-5 sm:p-6">
                  <h1 className="text-2xl font-bold text-gray-900">
                    Admin Dashboard
                  </h1>

                  {session && (
                    <p className="mt-1 text-gray-600">
                      Welcome back, {session.user.name || session.user.email}
                    </p>
                  )}
                </div>
              </motion.div>

              {/* Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {stats.map((stat) => (
                  <motion.div
                    key={stat.id}
                    variants={itemVariants}
                    className="bg-white overflow-hidden shadow rounded-lg"
                  >
                    <div className="px-6 py-5 sm:p-6">
                      <div className="flex items-center">
                        <div className={`flex-shrink-0 rounded-md p-3 bg-${stat.color}-100`}>
                          {stat.icon}
                        </div>
                        <div className="ml-5 w-0 flex-1">
                          <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">
                              {stat.title}
                            </dt>
                            <dd>
                              <div className="text-lg font-medium text-gray-900">
                                {stat.value.toLocaleString()}
                              </div>
                            </dd>
                          </dl>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className={`text-sm font-medium ${
                          stat.change >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {stat.change >= 0 ? '↑' : '↓'} {Math.abs(stat.change)}% from last month
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Quick actions */}
              <motion.div
                variants={itemVariants}
                className="bg-white overflow-hidden shadow rounded-lg"
              >
                <div className="px-6 py-5 sm:p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">
                    Quick Actions
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <Link
                      href="/admin/users"
                      className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FiUsers className="mr-2" /> Manage Users
                    </Link>
                    <Link
                      href="/admin/blog"
                      className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      <FiFileText className="mr-2" /> Manage Blog
                    </Link>
                    <Link
                      href="/admin/tools/list"
                      className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                    >
                      <FiTool className="mr-2" /> Manage Tools
                    </Link>
                    <Link
                      href="/admin/analytics"
                      className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <FiActivity className="mr-2" /> Analytics
                    </Link>
                    <Link
                      href="/admin/settings"
                      className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    >
                      <FiSettings className="mr-2" /> Settings
                    </Link>
                  </div>
                </div>
              </motion.div>

              {/* Recent activity and content sections */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent activity */}
                <motion.div
                  variants={itemVariants}
                  className="bg-white overflow-hidden shadow rounded-lg"
                >
                  <div className="px-6 py-5 sm:p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-medium text-gray-900">
                        Recent Activity
                      </h2>
                      <Link
                        href="/admin/activity"
                        className="text-sm font-medium text-blue-600 hover:text-blue-500 flex items-center"
                      >
                        View all <FiArrowRight className="ml-1" />
                      </Link>
                    </div>

                    <div className="space-y-4">
                      {recentActivities.map((activity) => (
                        <div
                          key={activity.id}
                          className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div className={`p-2 rounded-lg mr-3 ${getActivityColor(activity.type)}`}>
                            {getActivityIcon(activity.type)}
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">{activity.title}</p>
                            <p className="text-sm text-gray-500">{activity.description}</p>
                            <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>

                {/* Content management */}
                <motion.div
                  variants={itemVariants}
                  className="bg-white overflow-hidden shadow rounded-lg"
                >
                  <div className="px-6 py-5 sm:p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-medium text-gray-900">
                        Content Management
                      </h2>
                      <Link
                        href="/admin/blog/editor"
                        className="text-sm font-medium text-blue-600 hover:text-blue-500 flex items-center"
                      >
                        <FiPlusCircle className="mr-1" /> New Post
                      </Link>
                    </div>

                    <div className="space-y-4">
                      <Link
                        href="/admin/blog"
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center">
                          <div className="bg-green-100 p-2 rounded-lg mr-3 text-green-600">
                            <FiFileText className="h-5 w-5" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">Blog Posts</p>
                            <p className="text-sm text-gray-500">Manage all blog content</p>
                          </div>
                        </div>
                        <FiArrowRight className="text-gray-400" />
                      </Link>

                      <Link
                        href="/admin/comments"
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center">
                          <div className="bg-yellow-100 p-2 rounded-lg mr-3 text-yellow-600">
                            <FiMessageSquare className="h-5 w-5" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">Comments</p>
                            <p className="text-sm text-gray-500">Moderate user comments</p>
                          </div>
                        </div>
                        <FiArrowRight className="text-gray-400" />
                      </Link>

                      <Link
                        href="/admin/tools/list"
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center">
                          <div className="bg-purple-100 p-2 rounded-lg mr-3 text-purple-600">
                            <FiTool className="h-5 w-5" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">Tools</p>
                            <p className="text-sm text-gray-500">Manage tool configurations</p>
                          </div>
                        </div>
                        <FiArrowRight className="text-gray-400" />
                      </Link>

                      <Link
                        href="/admin/analytics"
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center">
                          <div className="bg-indigo-100 p-2 rounded-lg mr-3 text-indigo-600">
                            <FiActivity className="h-5 w-5" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">Analytics</p>
                            <p className="text-sm text-gray-500">View site analytics and statistics</p>
                          </div>
                        </div>
                        <FiArrowRight className="text-gray-400" />
                      </Link>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </RequireRole>
  );
}
