import { ComponentType } from 'react';

const toolCache = new Map<string, ComponentType>();

export async function loadToolComponent(toolPath: string): Promise<ComponentType | null> {
  // Check cache first
  if (toolCache.has(toolPath)) {
    return toolCache.get(toolPath)!;
  }

  try {
    // Convert kebab-case to PascalCase (pdf-rotate → PdfRotate.tsx)
    const componentName = toolPath
      .split('-')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join('');

    // Dynamically import the component
    const module = await import(`@/components/tools/converters/${componentName}`);

    // Ensure it's a valid React component
    const Component: ComponentType | undefined = module.default;

    if (!Component) {
      throw new Error(`Component ${componentName} has no default export`);
    }

    // Cache the component for future use
    toolCache.set(toolPath, Component);
    return Component;

  } catch (error: any) {
    console.error(`Tool loading failed for "${toolPath}":`, error.message || error);
    return null;
  }
}




//model
import mongoose from 'mongoose';

const toolSchema = new mongoose.Schema({
  name: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  description: { type: String, required: true },
  category: { type: String, required: true },
}, { timestamps: true });

export default mongoose.models.Tool || mongoose.model('Tool', toolSchema);



export interface Tool {
    id: string;
    title: string;
    description: string;
    icon: string;
    category: 'pdf' | 'office' | 'image';
    inputFormat?: string;
    outputFormat?: string;
    componentName: string;
  }
  
  export const ALL_TOOLS: Tool[] = [
    {
      id: "compress-pdf",
      title: "Compress PDF",
      description: "Reduce PDF file size while preserving quality",
      icon: "📦",
      category: "pdf",
      inputFormat: "PDF",
      outputFormat: "PDF",
      componentName: "CompressPdfConverter"
    },
    {
      id: "merge-pdf",
      title: "Merge PDF",
      description: "Combine multiple PDFs into one document",
      icon: "🧩",
      category: "pdf",
      inputFormat: "PDF",
      outputFormat: "PDF",
      componentName: "MergePdfConverter"
    }, 12 more .............]

src/components/tools/fileUploader.tsx && genericConverter.tsx & ToolCard.tsx & ToolLayout.tsx   && converters/CompressPdfConverter.tsx 13 more.........


    src/app/tools/page     
    import { ALL_TOOLS } from '@/data/tools';
import type { Tool } from '@/data/tools';
import ToolCard from '@/components/tools/ToolCard';
import Header from '@/components/layout/Header';

interface CategorizedTools {
  [key: string]: Tool[];
}

const categoryLabels: { [key: string]: string } = {
  pdf: 'PDF Tools',
  office: 'Office Conversions',
  image: 'Image Tools',
};

const categorizeTools = (tools: Tool[]): CategorizedTools => {
  return tools.reduce((categories, tool) => {
    if (!categories[tool.category]) {
      categories[tool.category] = [];
    }
    categories[tool.category].push(tool);
    return categories;
  }, {} as CategorizedTools);
};

export default function ToolsPage() {
  const categorizedTools = categorizeTools(ALL_TOOLS);

  return (
    <>
      <Header />
      <div className="py-12 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl lg:text-6xl mb-4">
            PDF Conversion Tools
          </h1>
          <p className="max-w-3xl mx-auto text-xl text-gray-600">
            Convert, edit, and transform your documents with our powerful online tools
          </p>
        </div>

        {/* Render categorized tools */}
        {Object.keys(categorizedTools).map((category) => (
          <section key={category} className="mb-16">
            <div className="flex items-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 flex-1">
                {categoryLabels[category] || category}
              </h2>
              <div className="h-px flex-1 bg-gray-200 ml-4"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categorizedTools[category].map((tool, index) => (
                <ToolCard
                  key={tool.id}
                  title={tool.title}
                  description={tool.description}
                  icon={tool.icon}
                  // componentName=''
                  href={`/tools/converters/${tool.id}`}
                  inputFormat={tool.inputFormat}
                  outputFormat={tool.outputFormat}
                  category={category}
                  delay={index * 0.05}
                />
              ))}
            </div>
          </section>
        ))}

        {/* Request new tool section */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-3">
            Need something different?
          </h3>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
            We're constantly expanding our toolset. Let us know what conversions you need!
          </p>
          <button className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
            Request a New Tool
          </button>
        </div>
      </div>
    </>
  );
}

src/app/tools/[tools]/page
import { ALL_TOOLS } from '@/data/tools';
import type { Tool } from '@/data/tools';
import ToolCard from '@/components/tools/ToolCard';
import Header from '@/components/layout/Header';

interface CategorizedTools {
  [key: string]: Tool[];
}

const categoryLabels: { [key: string]: string } = {
  pdf: 'PDF Tools',
  office: 'Office Conversions',
  image: 'Image Tools',
};

const categorizeTools = (tools: Tool[]): CategorizedTools => {
  return tools.reduce((categories, tool) => {
    if (!categories[tool.category]) {
      categories[tool.category] = [];
    }
    categories[tool.category].push(tool);
    return categories;
  }, {} as CategorizedTools);
};

export default function ToolsPage() {
  const categorizedTools = categorizeTools(ALL_TOOLS);

  return (
    <>
      <Header />
      <div className="py-12 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl lg:text-6xl mb-4">
            PDF Conversion Tools
          </h1>
          <p className="max-w-3xl mx-auto text-xl text-gray-600">
            Convert, edit, and transform your documents with our powerful online tools
          </p>
        </div>

        {/* Render categorized tools */}
        {Object.keys(categorizedTools).map((category) => (
          <section key={category} className="mb-16">
            <div className="flex items-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 flex-1">
                {categoryLabels[category] || category}
              </h2>
              <div className="h-px flex-1 bg-gray-200 ml-4"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categorizedTools[category].map((tool, index) => (
                <ToolCard
                  key={tool.id}
                  title={tool.title}
                  description={tool.description}
                  icon={tool.icon}
                  // componentName=''
                  href={`/tools/converters/${tool.id}`}
                  inputFormat={tool.inputFormat}
                  outputFormat={tool.outputFormat}
                  category={category}
                  delay={index * 0.05}
                />
              ))}
            </div>
          </section>
        ))}

        {/* Request new tool section */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-3">
            Need something different?
          </h3>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-6">
            We're constantly expanding our toolset. Let us know what conversions you need!
          </p>
          <button className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200">
            Request a New Tool
          </button>
        </div>
      </div>
    </>
  );
}
