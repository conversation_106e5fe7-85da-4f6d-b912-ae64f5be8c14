'use client';

import Link from "next/link";
import { useTheme } from "@/hooks/useTheme";
import { motion } from "framer-motion";
import { FiArrowRight, FiSearch, FiBookOpen, FiTag, FiCalendar, FiUser, FiMail } from "react-icons/fi";
import { useState, useEffect, useRef } from "react";
import { getAllPosts, getCategories, searchPosts } from "@/services/blogService";
import { BlogLayout } from "@/components/blog/BlogLayout";


export default function AllBlogsPage() {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredPosts, setFilteredPosts] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [totalPostCount, setTotalPostCount] = useState(0);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Get all blog posts and categories
  const allPosts = getAllPosts();
  const categories = getCategories();

  // Initialize filtered posts and calculate total post count
  useEffect(() => {
    // Apply category filter if selected
    let posts = selectedCategory
      ? allPosts.filter(post => post.category === selectedCategory)
      : allPosts;

    // Apply search filter if query exists
    if (searchQuery.trim() !== '') {
      posts = searchPosts(searchQuery);
      // If category is selected, filter the search results by category
      if (selectedCategory) {
        posts = posts.filter(post => post.category === selectedCategory);
      }
    }

    setFilteredPosts(posts);
    setTotalPostCount(allPosts.length);
  }, [searchQuery, selectedCategory, allPosts]);

  // Handle category selection
  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category);
    // Scroll to results when category is selected
    setTimeout(() => {
      resultsRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);
  };

  // Handle search submission
  const handleSearch = () => {
    // Scroll to results when search button is clicked
    resultsRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  return (
    <main className="min-h-screen bg-var-bg-primary text-var-text-primary transition-colors duration-300" style={{
      backgroundColor: 'var(--bg-primary)',
      color: 'var(--text-primary)'
    }}>
      {/* Hero Section */}
      <section className="relative overflow-hidden transition-colors duration-300 pt-24 pb-16" style={{
        backgroundColor: 'var(--bg-primary)'
      }}>
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/5 dark:from-purple-900/20 dark:to-blue-900/10" />

        {/* Animated background shapes */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          <motion.div
            className="absolute top-20 right-10 w-64 h-64 rounded-full bg-purple-400 dark:bg-purple-600 blur-3xl"
            animate={{
              x: [0, -30, 0],
              y: [0, 20, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              repeat: Infinity,
              duration: 8,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-20 left-10 w-72 h-72 rounded-full bg-blue-400 dark:bg-blue-600 blur-3xl"
            animate={{
              x: [0, 30, 0],
              y: [0, -20, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              repeat: Infinity,
              duration: 10,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-400 dark:to-blue-400">
                All Blog Articles
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-gray-700 dark:text-gray-300">
                Insights, guides, and tips from our experts
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap justify-center gap-4 mb-12"
            >
              <div className="flex items-center px-4 py-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300">
                <FiBookOpen className="mr-2" /> {totalPostCount} Articles
              </div>
              <div className="flex items-center px-4 py-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                <FiTag className="mr-2" /> {categories.length} Categories
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="relative max-w-2xl mx-auto mb-8"
            >
              <div className="flex items-center p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700">
                <FiSearch className="ml-3 mr-2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search for articles..."
                  className="w-full py-2 px-3 bg-transparent border-none focus:outline-none text-gray-800 dark:text-gray-200"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <button
                  className="ml-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-full transition-all duration-200 transform hover:scale-105 active:scale-95"
                  onClick={handleSearch}
                >
                  Search
                </button>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Wave divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
            <path
              fill="currentColor"
              fillOpacity="1"
              className="text-gray-50 dark:text-gray-800"
              d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"
            ></path>
          </svg>
        </div>
      </section>

      {/* Category Filter Section */}
      <section className="py-8 transition-colors duration-300" style={{
        backgroundColor: 'var(--bg-secondary)'
      }}>
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-wrap justify-center gap-3"
          >
            <motion.button
              whileHover={{ y: -2 }}
              whileTap={{ scale: 0.95 }}
              className={`px-4 py-2 rounded-full transition-all duration-200 ${
                selectedCategory === null
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
              onClick={() => handleCategorySelect(null)}
            >
              All Categories
            </motion.button>

            {categories.map((category, index) => (
              <motion.button
                key={category.name}
                whileHover={{ y: -2 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 + (index * 0.05) }}
                className={`px-4 py-2 rounded-full transition-all duration-200 ${
                  selectedCategory === category.name
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
                onClick={() => handleCategorySelect(category.name)}
              >
                {category.name} ({category.count})
              </motion.button>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Blog Posts Section */}
      <section ref={resultsRef} id="blog-results" className="py-16 transition-colors duration-300" style={{
        backgroundColor: 'var(--bg-secondary)'
      }}>
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold mb-6 text-center"
            style={{ color: 'var(--text-primary)' }}
          >
            {searchQuery.trim() !== ''
              ? `Search Results for "${searchQuery}"`
              : selectedCategory
                ? `Articles in ${selectedCategory}`
                : "All Articles"}
          </motion.h2>

          {/* Search results info */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-8 text-center"
          >
            {searchQuery.trim() !== '' ? (
              filteredPosts.length === 0 ? (
                <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>
                  No articles found matching your search.
                </p>
              ) : (
                <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>
                  Found <span className="font-semibold text-purple-600 dark:text-purple-400">{filteredPosts.length}</span> article{filteredPosts.length !== 1 ? 's' : ''}
                </p>
              )
            ) : (
              selectedCategory ? (
                <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>
                  Showing <span className="font-semibold text-purple-600 dark:text-purple-400">{filteredPosts.length}</span> article{filteredPosts.length !== 1 ? 's' : ''} in {selectedCategory}
                </p>
              ) : (
                <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>
                  Showing all <span className="font-semibold text-purple-600 dark:text-purple-400">{filteredPosts.length}</span> article{filteredPosts.length !== 1 ? 's' : ''}
                </p>
              )
            )}
          </motion.div>

          {/* Blog posts grid */}
          {filteredPosts.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center py-12"
            >
              <div className="mx-auto w-24 h-24 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-6">
                <FiSearch className="w-10 h-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-medium mb-2" style={{ color: 'var(--text-primary)' }}>No articles found</h3>
              <p style={{ color: 'var(--text-secondary)' }}>
                Try adjusting your search or category filter to find what you're looking for.
              </p>
            </motion.div>
          ) : (
            <motion.div
              initial="hidden"
              animate="visible"
              variants={{
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: {
                    staggerChildren: 0.05,
                    delayChildren: 0.1
                  }
                }
              }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {filteredPosts.map((post, index) => (
                <BlogCard
                  key={post.id}
                  post={post}
                  index={index}
                  theme={theme}
                />
              ))}
            </motion.div>
          )}
        </div>
      </section>


    </main>
  );
}

// Blog Card Component
const BlogCard = ({ post, index, theme }: { post: any, index: number, theme: string }) => {
  const isDark = theme === 'dark';

  // Calculate staggered delay based on index
  const delay = 0.05 * (index % 9); // Reset delay every 9 items for better UX

  return (
    <motion.article
      variants={{
        hidden: { opacity: 0, y: 30 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.5,
            delay,
            ease: [0.25, 0.1, 0.25, 1.0]
          }
        }
      }}
      whileHover={{
        y: -10,
        transition: { duration: 0.3 }
      }}
      className="rounded-xl border overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300"
      style={{
        backgroundColor: isDark ? 'var(--bg-secondary)' : 'white',
        borderColor: isDark ? '#374151' : '#e5e7eb'
      }}
    >
      <Link href={`/blog/${post.id}`} className="block">
        <div className="relative overflow-hidden h-52">
          <motion.div
            whileHover={{ scale: 1.08 }}
            transition={{ duration: 0.6 }}
            className="h-full w-full"
          >
            <img
              src={post.image}
              alt={post.title}
              className="w-full h-full object-cover"
            />
          </motion.div>
          <div className="absolute top-4 left-4">
            <span className="px-3 py-1 text-xs font-medium rounded-full"
              style={{
                backgroundColor: isDark ? 'rgba(124, 58, 237, 0.8)' : 'rgba(124, 58, 237, 0.9)',
                color: 'white',
                backdropFilter: 'blur(4px)'
              }}
            >
              {post.category}
            </span>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-black/70 to-transparent" />
        </div>
      </Link>

      <div className="p-6">
        <div className="flex items-center text-xs gap-4 mb-3" style={{ color: 'var(--text-secondary)' }}>
          <div className="flex items-center gap-1">
            <FiCalendar className="h-3 w-3" />
            <span>{post.date}</span>
          </div>
          <div className="flex items-center gap-1">
            <FiUser className="h-3 w-3" />
            <span>{post.author}</span>
          </div>
        </div>

        <Link href={`/blog/${post.id}`}>
          <h3 className="text-xl font-bold mb-3 hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
            style={{ color: 'var(--text-primary)' }}
          >
            {post.title}
          </h3>
        </Link>

        <p className="mb-4 line-clamp-2" style={{ color: 'var(--text-secondary)' }}>
          {post.description}
        </p>

        <motion.div
          whileHover={{ x: 5 }}
          transition={{ duration: 0.2 }}
          className="flex items-center"
        >
          <Link
            href={`/blog/${post.id}`}
            className="font-medium inline-flex items-center text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300"
          >
            Read Article <FiArrowRight className="ml-2" />
          </Link>
        </motion.div>
      </div>
    </motion.article>
  );
};