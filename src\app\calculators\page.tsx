"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function CalculatorsPage() {
  const router = useRouter();
  
  // Redirect to the new calculator page
  useEffect(() => {
    router.replace("/calculators/all");
  }, [router]);
  
  // Show loading state while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <h2 className="text-2xl font-bold mb-2">Redirecting...</h2>
        <p className="text-muted-foreground">Taking you to the calculators page</p>
      </div>
    </div>
  );
}
