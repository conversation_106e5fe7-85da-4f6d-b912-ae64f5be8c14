'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Check, ChevronsUpDown, Plus, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { toast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import logger from '@/lib/secureLogger';

interface Category {
  _id: string;
  name: string;
  slug?: string;
  description?: string;
}

interface CategorySelectProps {
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
}

export function CategorySelect({ value, onChange, placeholder = 'Select categories...' }: CategorySelectProps) {
  const [open, setOpen] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [creatingCategory, setCreatingCategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>(value || []);
  const inputRef = useRef<HTMLInputElement>(null);

  // Fetch categories on mount
  useEffect(() => {
    fetchCategories();
  }, []);

  // Update internal state when value prop changes (only if different)
  useEffect(() => {
    // Check if arrays are different before updating state
    const areArraysEqual =
      value.length === selectedCategories.length &&
      value.every(item => selectedCategories.includes(item));

    if (!areArraysEqual) {
      setSelectedCategories(value || []);
    }
  }, [value, selectedCategories]);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/categories');

      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      const data = await response.json();
      setCategories(data);
    } catch (error) {
      logger.error('Error fetching categories');
      toast({
        title: 'Error',
        description: 'Failed to load categories',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const createCategory = async () => {
    if (!newCategoryName.trim()) {
      toast({
        title: 'Error',
        description: 'Category name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    try {
      setCreatingCategory(true);

      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newCategoryName.trim() }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to create category');
      }

      const newCategory = await response.json();

      // Add the new category to the list
      setCategories(prev => [...prev, newCategory]);

      // Select the new category and notify parent
      const newSelectedCategories = [...selectedCategories, newCategory._id];
      setSelectedCategories(newSelectedCategories);
      onChange(newSelectedCategories);

      // Reset the input
      setNewCategoryName('');
      setCreatingCategory(false);

      toast({
        title: 'Success',
        description: `Category "${newCategory.name}" created`,
      });
    } catch (error: any) {
      logger.error('Error creating category');
      toast({
        title: 'Error',
        description: error?.message || 'Failed to create category',
        variant: 'destructive',
      });
    } finally {
      setCreatingCategory(false);
    }
  };

  const toggleCategory = (categoryId: string) => {
    const newCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter(id => id !== categoryId)
      : [...selectedCategories, categoryId];

    setSelectedCategories(newCategories);
    onChange(newCategories);
  };

  // Use a memoized callback for onOpenChange to prevent recreation on each render
  const handleOpenChange = useCallback((isOpen: boolean) => {
    setOpen(isOpen);
  }, []);

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedCategories.length > 0
            ? `${selectedCategories.length} categor${selectedCategories.length === 1 ? 'y' : 'ies'} selected`
            : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search categories..." />
          <CommandList>
            <CommandEmpty>
              {loading ? (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading...
                </div>
              ) : (
                <div className="py-6 text-center text-sm">No categories found</div>
              )}
            </CommandEmpty>
            <CommandGroup>
              {categories.map((category) => (
                <CommandItem
                  key={category._id}
                  value={category.name}
                  onSelect={() => toggleCategory(category._id)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedCategories.includes(category._id) ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {category.name}
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup>
              <div className="p-2 flex items-center gap-2">
                <Input
                  ref={inputRef}
                  placeholder="Add new category..."
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      createCategory();
                    }
                  }}
                  className="h-8 flex-1"
                />
                <Button
                  size="sm"
                  disabled={creatingCategory || !newCategoryName.trim()}
                  onClick={createCategory}
                  className="h-8 px-2"
                >
                  {creatingCategory ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Plus className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
