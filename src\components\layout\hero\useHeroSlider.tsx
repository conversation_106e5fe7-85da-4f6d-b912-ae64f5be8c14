// components/useHeroSlider.js
import { useEffect, useState } from "react";

export function useHeroSlider(slides: any) {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const timeout = setTimeout(() => {
      const interval = setInterval(() => {
        setActiveIndex((prevIndex) => (prevIndex + 1) % slides.length);
      }, 10000);
      return () => clearInterval(interval);
    }, 1000);
    return () => clearTimeout(timeout);
  }, [slides.length]);

  return { activeIndex, setActiveIndex };
}
