import { getToken } from "next-auth/jwt";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

const secret = process.env.NEXTAUTH_SECRET;

export async function middleware(req: NextRequest) {
  const token = await getToken({ req, secret });
  const pathname = req.nextUrl.pathname;

  // Handle API routes - add user headers for authenticated requests
  if (pathname.startsWith("/api")) {
    const requestHeaders = new Headers(req.headers);

    if (token) {
      requestHeaders.set('x-user-id', token.sub as string);
      requestHeaders.set('x-user-role', token.role as string || 'user');
    }

    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }

  // Public routes accessible by anyone
  const publicRoutes = ["/", "/tools", "/blog", "/login", "/register", "/calculators"];

  // Allow access to public routes and their sub-routes without token
  if (publicRoutes.some(route => pathname === route || pathname.startsWith(route + "/"))) {
    return NextResponse.next();
  }

  // Protect /user routes for logged-in users only
  if (pathname.startsWith("/user")) {
    if (!token) {
      // Redirect to login if no valid token
      return NextResponse.redirect(new URL("/login", req.url));
    }
    return NextResponse.next();
  }

  // Protect /admin routes for admin users only
  if (pathname.startsWith("/admin")) {
    if (!token) {
      return NextResponse.redirect(new URL("/login", req.url));
    }

    if (token.role !== "admin") {
      // Redirect non-admin users to home
      return NextResponse.redirect(new URL("/", req.url));
    }

    return NextResponse.next();
  }

  // Allow all other routes by default (don't block unknown routes)
  return NextResponse.next();
}

// Apply middleware only to specific route patterns
export const config = {
  matcher: [
    "/admin/:path*",
    "/user/:path*",
    "/api/:path*", // Add API routes to middleware
    "/((?!_next/static|_next/image|favicon.ico).*)", // Apply to all routes except Next.js internals
  ],
};
