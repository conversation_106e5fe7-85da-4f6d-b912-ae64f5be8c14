import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import { z } from "zod";

// Schema validation
const BlogPostSchema = z.object({
  title: z.string().min(10),
  content: z.string().min(100),
  slug: z.string().regex(/^[a-z0-9-]+$/),
  status: z.enum(["draft", "scheduled", "published", "archived"]),
  scheduledAt: z.coerce.date().optional().nullable(),
  image: z.string().url().optional(),
  excerpt: z.string().max(200).optional(),
});

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const parsedParams = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      status: searchParams.get("status"),
      search: searchParams.get("search"),
    };

    const query: any = {};
    if (parsedParams.status) query.status = parsedParams.status;
    if (parsedParams.search) {
      query.$or = [
        { title: { $regex: parsedParams.search, $options: "i" } },
        { content: { $regex: parsedParams.search, $options: "i" } },
      ];
    }

    const [posts, total] = await Promise.all([
      BlogPost.find(query)
        .sort({ createdAt: -1 })
        .skip((parsedParams.page - 1) * parsedParams.limit)
        .limit(parsedParams.limit)
        .populate("authorId", "name email"),
      BlogPost.countDocuments(query),
    ]);

    return NextResponse.json({
      data: posts,
      pagination: {
        total,
        page: parsedParams.page,
        limit: parsedParams.limit,
        totalPages: Math.ceil(total / parsedParams.limit),
      },
    });
  } catch (error) {
    console.error("GET /api/blog error:", error);
    return NextResponse.json(
      { error: "Failed to fetch posts" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    // Get user ID from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    if (!userId) return unauthorizedResponse();

    const body = await request.json();
    const validation = BlogPostSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error },
        { status: 400 }
      );
    }

    const existingSlug = await BlogPost.findOne({ slug: body.slug });
    if (existingSlug) {
      return NextResponse.json(
        { error: "Slug already exists" },
        { status: 409 }
      );
    }

    const newPost = await BlogPost.create({
      ...validation.data,
      authorId: userId,
      scheduledAt: validation.data.status === "scheduled"
        ? validation.data.scheduledAt
        : null,
    });

    return NextResponse.json(newPost, { status: 201 });
  } catch (error) {
    console.error("POST /api/blog error:", error);
    return serverErrorResponse("Failed to create post");
  }
}

// PUT and DELETE handlers
export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase();

    // Get user ID from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    if (!userId) return unauthorizedResponse();

    const { searchParams } = new URL(request.url);
    const postId = searchParams.get("id");
    if (!postId) return invalidRequestResponse("Missing post ID");

    const body = await request.json();
    const validation = BlogPostSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error },
        { status: 400 }
      );
    }

    const existingPost = await BlogPost.findById(postId);
    if (!existingPost) return notFoundResponse("Post not found");

    // Check if user is the author or an admin
    const userRole = request.headers.get("x-user-role");
    if (existingPost.authorId.toString() !== userId && userRole !== "admin") {
      return unauthorizedResponse();
    }

    const updatedPost = await BlogPost.findByIdAndUpdate(postId, validation.data, {
      new: true,
    });

    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error("PUT /api/blog error:", error);
    return serverErrorResponse("Failed to update post");
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase();

    // Get user ID from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    if (!userId) return unauthorizedResponse();

    const { searchParams } = new URL(request.url);
    const postId = searchParams.get("id");
    if (!postId) return invalidRequestResponse("Missing post ID");

    const post = await BlogPost.findById(postId);
    if (!post) return notFoundResponse("Post not found");

    // Check if user is the author or an admin
    const userRole = request.headers.get("x-user-role");
    if (post.authorId.toString() !== userId && userRole !== "admin") {
      return unauthorizedResponse();
    }

    await BlogPost.findByIdAndDelete(postId);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("DELETE /api/blog error:", error);
    return serverErrorResponse("Failed to delete post");
  }
}

// Helper functions
function unauthorizedResponse() {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
}

function notFoundResponse(message: string) {
  return NextResponse.json({ error: message }, { status: 404 });
}

function invalidRequestResponse(message: string) {
  return NextResponse.json({ error: message }, { status: 400 });
}

function serverErrorResponse(message: string) {
  return NextResponse.json({ error: message }, { status: 500 });
}
